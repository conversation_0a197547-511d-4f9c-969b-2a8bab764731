// Simple test script for Freepik API integration (without styling)
// Run this with: node test-freepik-simple.js

import dotenv from 'dotenv';
// Note: In Node.js 18+, fetch is built-in. For older versions, install node-fetch
const fetch = globalThis.fetch || (await import('node-fetch')).default;

// Load environment variables
dotenv.config();

const FREEPIK_API_KEY = process.env.FREEPIK_API_KEY;

async function testFreepikAPISimple() {
  if (!FREEPIK_API_KEY || FREEPIK_API_KEY === 'your_freepik_api_key_here') {
    console.error('❌ Freepik API key not configured');
    console.log('Please set FREEPIK_API_KEY in your .env file');
    return;
  }

  console.log('🧪 Testing Freepik API integration (simple version)...');

  // Simple prompt without complex styling
  const prompt = "A young knight in shining armor in a medieval castle courtyard at sunset, epic dramatic lighting, high quality, detailed";

  console.log('📝 Generated prompt:', prompt);

  // Minimal payload - just the essentials
  const freepikPayload = {
    prompt: prompt,
    num_images: 1,
    image: {
      size: "landscape_16_9"
    },
    filter_nsfw: true
  };

  try {
    console.log('🚀 Calling Freepik API...');
    
    const response = await fetch('https://api.freepik.com/v1/ai/text-to-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': FREEPIK_API_KEY,
      },
      body: JSON.stringify(freepikPayload),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('❌ Freepik API error:', data);
      console.log('Status:', response.status);
      return;
    }

    if (data.data && data.data[0] && data.data[0].base64) {
      console.log('✅ Image generated successfully!');
      console.log('📊 Image metadata:');
      console.log('  - Size:', data.meta.image.size);
      console.log('  - Dimensions:', `${data.meta.image.width}x${data.meta.image.height}`);
      console.log('  - Seed:', data.meta.seed);
      console.log('  - Guidance Scale:', data.meta.guidance_scale);
      console.log('  - Base64 length:', data.data[0].base64.length, 'characters');
      console.log('  - NSFW detected:', data.data[0].has_nsfw);
      
      // Save base64 to file for testing (optional)
      const fs = await import('fs');
      const base64Data = data.data[0].base64;
      const buffer = Buffer.from(base64Data, 'base64');
      fs.writeFileSync('test-generated-image-simple.png', buffer);
      console.log('💾 Image saved as test-generated-image-simple.png');
      
    } else {
      console.error('❌ Invalid response structure:', data);
    }

  } catch (error) {
    console.error('❌ Error testing Freepik API:', error.message);
  }
}

// Run the test
testFreepikAPISimple().catch(console.error);
