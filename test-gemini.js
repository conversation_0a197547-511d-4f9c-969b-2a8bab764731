// This is a simple test script to verify that the Gemini API integration works
// Run with: node test-gemini.js

import { GoogleGenAI } from '@google/genai';
import dotenv from 'dotenv';

// Load environment variables from .env.local
dotenv.config({ path: '.env' });

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

if (!GOOGLE_API_KEY) {
  console.error('GOOGLE_API_KEY is not set in .env.local');
  process.exit(1);
}

async function testGeminiAPI() {
  try {
    // Initialize the Gemini API client
    const genAI = new GoogleGenAI({ apiKey: GOOGLE_API_KEY });

    // Generate a simple response
    const result = await genAI.models.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: 'Write a short poem about AI.',
      config: {
        temperature: 0.7,
        maxOutputTokens: 200,
      },
    });

    console.log('API Response:');
    console.log('Full result:', JSON.stringify(result, null, 2));

    // Extract text from the response based on the SDK structure
    let text = '';

    try {
      // The structure might be different depending on the SDK version
      if (result.candidates && result.candidates.length > 0) {
        const candidate = result.candidates[0];
        if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
          text = candidate.content.parts[0].text || '';
        }
      }

      // If we couldn't extract text using the expected structure, try to stringify the result
      if (!text) {
        text = JSON.stringify(result);
      }
    } catch (e) {
      console.error('Error extracting text from response:', e);
      text = 'Unable to extract text from response';
    }

    console.log('\nExtracted response text:');
    console.log(text);

    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing Gemini API:', error);
  }
}

testGeminiAPI();
