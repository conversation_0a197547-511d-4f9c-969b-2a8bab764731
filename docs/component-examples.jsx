/**
 * This file contains examples of how to use the design system
 * It is not meant to be imported, but rather to serve as a reference
 */

// Example of a Button component using the design system
const Button = ({ children, variant = 'primary', size = 'md', className = '', ...props }) => {
  // Map size to appropriate classes
  const sizeClasses = {
    sm: 'px-3 py-1 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg',
  };

  // Map variant to appropriate classes
  const variantClasses = {
    primary: 'bg-primary-green text-white hover:bg-primary-green-dark',
    secondary: 'bg-transparent border border-primary-green text-primary-green hover:bg-primary-green hover:text-white',
    outline: 'bg-transparent border border-gray-300 text-gray-700 hover:border-primary-green hover:text-primary-green',
    text: 'bg-transparent text-primary-green hover:underline',
  };

  return (
    <button
      className={`
        rounded-md font-medium transition-all duration-normal
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
      {...props}
    >
      {children}
    </button>
  );
};

// Example of a Card component using the design system
const Card = ({ children, className = '', padding = 'md', shadow = 'md', ...props }) => {
  // Map padding to appropriate classes
  const paddingClasses = {
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8',
  };

  // Map shadow to appropriate classes
  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
  };

  return (
    <div
      className={`
        bg-white rounded-lg
        ${paddingClasses[padding]}
        ${shadowClasses[shadow]}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

// Example of an Input component using the design system
const Input = ({ label, error, className = '', ...props }) => {
  return (
    <div className="mb-4">
      {label && (
        <label className="block text-gray-700 font-medium mb-2">
          {label}
        </label>
      )}
      <input
        className={`
          w-full px-4 py-2 border rounded-md focus:outline-none
          ${error ? 'border-error focus:ring-2 focus:ring-error/50' : 'border-gray-300 focus:ring-2 focus:ring-primary-green/50 focus:border-transparent'}
          ${className}
        `}
        {...props}
      />
      {error && (
        <p className="mt-1 text-error text-sm">{error}</p>
      )}
    </div>
  );
};

// Example of a Typography component using the design system
const Typography = ({ variant = 'body', children, className = '', ...props }) => {
  // Map variant to appropriate element and classes
  const variantMap = {
    h1: {
      element: 'h1',
      className: 'font-libre text-4xl font-bold mb-4',
    },
    h2: {
      element: 'h2',
      className: 'font-libre text-3xl font-bold mb-3',
    },
    h3: {
      element: 'h3',
      className: 'font-libre text-2xl font-bold mb-2',
    },
    h4: {
      element: 'h4',
      className: 'font-libre text-xl font-bold mb-2',
    },
    subtitle: {
      element: 'p',
      className: 'font-figtree text-xl mb-4',
    },
    body: {
      element: 'p',
      className: 'font-figtree text-base mb-4',
    },
    small: {
      element: 'p',
      className: 'font-figtree text-sm mb-2',
    },
    caption: {
      element: 'span',
      className: 'font-figtree text-xs text-gray-500',
    },
    brand: {
      element: 'span',
      className: 'font-slackey text-2xl text-primary-green',
    },
  };

  const { element: Element, className: variantClassName } = variantMap[variant];

  return (
    <Element
      className={`${variantClassName} ${className}`}
      {...props}
    >
      {children}
    </Element>
  );
};

// Example of a Badge component using the design system
const Badge = ({ children, variant = 'default', className = '', ...props }) => {
  // Map variant to appropriate classes
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    primary: 'bg-primary-green/10 text-primary-green',
    success: 'bg-success/10 text-success',
    warning: 'bg-warning/10 text-warning',
    error: 'bg-error/10 text-error',
    info: 'bg-info/10 text-info',
  };

  return (
    <span
      className={`
        inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
        ${variantClasses[variant]}
        ${className}
      `}
      {...props}
    >
      {children}
    </span>
  );
};

// Example of a theme-aware component
const ThemeAwareComponent = ({ children }) => {
  return (
    <div className="light-theme md:dark-theme">
      {/* This component will use light theme on mobile and dark theme on desktop */}
      <div className="bg-card-bg text-foreground border border-card-border p-4 rounded-md">
        {children}
      </div>
    </div>
  );
};

// Example of using spacing variables
const SpacingExample = () => {
  return (
    <div className="space-y-md">
      <div className="p-sm">Small padding</div>
      <div className="p-md">Medium padding</div>
      <div className="p-lg">Large padding</div>
      <div className="mt-xl">Extra large margin top</div>
    </div>
  );
};

// Example of using animations
const AnimationExample = () => {
  return (
    <div>
      <div className="animate-fade-in">Fade in animation</div>
      <div className="animate-slide-up">Slide up animation</div>
      <div className="animate-slide-down">Slide down animation</div>
      <div className="animate-pulse-slow">Slow pulse animation</div>
    </div>
  );
};

// Example of a complete page using the design system
const ExamplePage = () => {
  return (
    <div className="bg-background min-h-screen">
      <header className="bg-primary-green text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <Typography variant="brand">MoneyTales</Typography>
          <nav>
            <Button variant="text" className="text-white">Home</Button>
            <Button variant="text" className="text-white">Features</Button>
            <Button variant="text" className="text-white">About</Button>
            <Button variant="secondary" className="ml-4 border-white text-white hover:bg-white hover:text-primary-green">Login</Button>
          </nav>
        </div>
      </header>

      <main className="container mx-auto py-8 px-4">
        <section className="mb-12">
          <Typography variant="h1">Welcome to MoneyTales</Typography>
          <Typography variant="subtitle">Your journey to financial freedom starts here.</Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <Card>
              <Typography variant="h3">Track Expenses</Typography>
              <Typography>Keep track of your daily expenses and understand your spending habits.</Typography>
              <Button className="mt-4">Learn More</Button>
            </Card>
            
            <Card shadow="lg">
              <Typography variant="h3">Set Goals</Typography>
              <Typography>Set financial goals and track your progress over time.</Typography>
              <Button className="mt-4">Learn More</Button>
            </Card>
            
            <Card className="border border-primary-green">
              <Typography variant="h3">Get Insights</Typography>
              <Typography>Gain valuable insights into your financial health with AI-powered analysis.</Typography>
              <Button className="mt-4">Learn More</Button>
            </Card>
          </div>
        </section>
        
        <section className="mb-12">
          <Typography variant="h2">Features</Typography>
          
          <div className="mt-6 space-y-4">
            <div className="flex items-start">
              <Badge variant="primary" className="mt-1 mr-3">New</Badge>
              <div>
                <Typography variant="h4" className="mb-1">AI Financial Assistant</Typography>
                <Typography>Get personalized financial advice from our AI assistant.</Typography>
              </div>
            </div>
            
            <div className="flex items-start">
              <Badge variant="success" className="mt-1 mr-3">Popular</Badge>
              <div>
                <Typography variant="h4" className="mb-1">Budget Planner</Typography>
                <Typography>Create and manage your budget with our easy-to-use tools.</Typography>
              </div>
            </div>
          </div>
        </section>
        
        <section>
          <Typography variant="h2">Get Started</Typography>
          
          <Card className="max-w-md mt-6">
            <form>
              <Input label="Full Name" placeholder="Enter your name" />
              <Input label="Email" type="email" placeholder="Enter your email" />
              <Input 
                label="Password" 
                type="password" 
                placeholder="Create a password" 
                error="Password must be at least 8 characters"
              />
              
              <Button className="w-full mt-6">Sign Up</Button>
              
              <Typography variant="small" className="text-center mt-4">
                By signing up, you agree to our Terms of Service and Privacy Policy.
              </Typography>
            </form>
          </Card>
        </section>
      </main>
      
      <footer className="bg-gray-100 p-8 mt-12">
        <div className="container mx-auto">
          <Typography variant="brand" className="mb-6">MoneyTales</Typography>
          <Typography variant="small" className="text-gray-600">
            © {new Date().getFullYear()} MoneyTales. All rights reserved.
          </Typography>
        </div>
      </footer>
    </div>
  );
};

export {
  Button,
  Card,
  Input,
  Typography,
  Badge,
  ThemeAwareComponent,
  SpacingExample,
  AnimationExample,
  ExamplePage,
};
