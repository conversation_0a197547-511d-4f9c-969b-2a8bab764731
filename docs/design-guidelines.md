# The Money Tales Design Guidelines

This document provides guidelines on how to use the design system in The Money Tales application. Following these guidelines will ensure consistency across the application.

## Table of Contents

1. [Design Principles](#design-principles)
2. [Color System](#color-system)
3. [Typography](#typography)
4. [Spacing](#spacing)
5. [Components](#components)
6. [Adding New Design Elements](#adding-new-design-elements)

## Design Principles

The Money Tales design system is built on the following principles:

- **Consistency**: Use consistent design patterns throughout the application
- **Accessibility**: Ensure all designs are accessible to all users
- **Simplicity**: Keep designs simple and intuitive
- **Hierarchy**: Use visual hierarchy to guide users through the interface
- **Brand Identity**: Maintain the brand identity with the use of brand colors and typography

## Color System

### Primary Colors

The primary color palette consists of green shades that represent the brand:

- Primary Green: `#13824B` - Used for primary actions, headers, and key UI elements
- Primary Green Light: `#1A9D5C` - Used for hover states and secondary elements
- Primary Green Dark: `#0F6B3D` - Used for active states and tertiary elements

### Neutral Colors

- Cream: `#FFF4E0` - Main background color
- Cream Light: `#FFF9ED` - Secondary background color
- Cream Dark: `#F5E8D0` - Tertiary background color
- Black: `#1A1A1A` - Used for text and icons
- White: `#FFFFFF` - Used for text on dark backgrounds and card backgrounds

### Gray Scale

A range of gray colors from 100 (lightest) to 900 (darkest) for various UI elements.

### Feedback Colors

- Success: `#22C55E` - Used for success messages and indicators
- Warning: `#F59E0B` - Used for warning messages and indicators
- Error: `#EF4444` - Used for error messages and indicators
- Info: `#3B82F6` - Used for informational messages and indicators

### Theme Colors

The application uses semantic color variables for consistent theming:

- `--foreground`: Default text color (black in light theme, cream in dark theme)
- `--background`: Page background color
- `--text-primary`: Primary accent text color (green)
- `--text-secondary`: Secondary text color (gray)

### How to Use Colors

#### In CSS

Use CSS variables defined in `globals.css`:

```css
.my-element {
  background-color: var(--primary-green);
  color: var(--foreground);
}

.accent-text {
  color: var(--text-primary);
}

.secondary-text {
  color: var(--text-secondary);
}
```

#### In Tailwind

Use the color names directly in Tailwind classes:

```jsx
<button className="bg-primary-green text-white">Click Me</button>
```

Or use the theme colors:

```jsx
<div className="bg-background text-foreground">
  <span className="text-text-primary">Accent text</span>
  <p className="text-text-secondary">Secondary text</p>
</div>
```

#### In JavaScript

Import the colors and themes from `style_vars.js`:

```jsx
import { colors, themes } from '@/app/style_vars';

// Using direct color values
const myStyle = {
  backgroundColor: colors.primary.green,
  color: colors.neutral.black,
};

// Using theme values
const themedStyle = {
  backgroundColor: themes.light.background,
  color: themes.light.foreground,
  accentColor: themes.light.textPrimary,
};
```

## Typography

### Font Families

The Money Tales uses three main font families:

1. **Libre Baskerville** (`font-libre`): Used for headings and important text
2. **Figtree** (`font-figtree`): Used for body text and UI elements
3. **Slackey** (`font-slackey`): Used for the brand name and special elements

### Font Sizes

Font sizes follow a scale from xs to 6xl, defined in `style_vars.js`.

### How to Use Typography

#### In CSS

```css
.heading {
  font-family: var(--font-libre-baskerville);
  font-size: var(--text-2xl);
  font-weight: 700;
}
```

#### In Tailwind

```jsx
<h1 className="font-libre text-4xl font-bold">Heading</h1>
<p className="font-figtree text-base">Body text</p>
<span className="font-slackey text-2xl">Brand element</span>
```

#### For Responsive Typography

Use the fluid typography classes:

```jsx
<h1 className="text-fluid-xl font-bold">Responsive Heading</h1>
```

## Spacing

The spacing system uses a consistent scale from xs to 3xl:

- xs: 0.25rem (4px)
- sm: 0.5rem (8px)
- md: 1rem (16px)
- lg: 1.5rem (24px)
- xl: 2rem (32px)
- 2xl: 3rem (48px)
- 3xl: 4rem (64px)

### How to Use Spacing

#### In CSS

```css
.card {
  padding: var(--space-md);
  margin-bottom: var(--space-lg);
}
```

#### In Tailwind

```jsx
<div className="p-4 mb-6">Content</div>
```

#### In JavaScript

```jsx
import { spacing } from '@/app/style_vars';

const myStyle = {
  padding: spacing.md,
  marginBottom: spacing.lg,
};
```

## Components

The design system includes common component styles defined in `globals.css`:

### Buttons

```jsx
<button className="btn btn-primary">Primary Button</button>
<button className="btn btn-secondary">Secondary Button</button>
```

### Cards

```jsx
<div className="card">Card Content</div>
```

### Inputs

```jsx
<input className="input" type="text" placeholder="Enter text" />
```

## Adding New Design Elements

When adding new design elements to the application, follow these steps:

### 1. Check Existing Design Tokens

Before creating new design elements, check if there are existing design tokens in `style_vars.js` or CSS variables in `globals.css` that can be used.

### 2. Adding New Colors

If you need to add a new color:

1. Add the color variable to the `:root` section in `globals.css`:

```css
:root {
  /* existing colors */
  --new-color: #HEXCODE;
}
```

2. Add the color to the `colors` object in `style_vars.js`:

```js
export const colors = {
  // existing colors
  newCategory: {
    newColor: '#HEXCODE',
  },
};
```

3. If needed, add the color to the Tailwind configuration in `tailwind.config.js`:

```js
colors: {
  // existing colors
  'new-color': 'var(--new-color)',
},
```

### 3. Adding New Component Styles

To add new component styles:

1. Add the component style to the `@layer components` section in `globals.css`:

```css
@layer components {
  /* existing components */
  .new-component {
    @apply [tailwind classes];
  }
}
```

### 4. Adding New Typography Styles

To add new typography styles:

1. Add the style to the `typography` object in `style_vars.js` if it's a reusable style
2. Or create a new class in `globals.css` for specific typography needs

### 5. Document Your Additions

Always document new design elements by:

1. Adding comments in the code
2. Updating this design guidelines document
3. Sharing the changes with the team

By following these guidelines, you'll help maintain a consistent design system throughout The Money Tales application.
