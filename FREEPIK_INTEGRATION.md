# Freepik Integration for Alternate Scenario Images

This document explains how to set up and use the Freepik API integration for generating images in the alternate scenario feature.

## Overview

The Freepik integration automatically generates visual representations of your alternate scenarios based on the scene descriptions created by the Gemini AI. When a user generates an alternate scenario, the system will:

1. Extract scene visual descriptions from the JSON output
2. Create an optimized prompt for image generation
3. Call the Freepik API to generate a high-quality image
4. Display the generated image alongside the scenario text

## Setup Instructions

### 1. Get a Freepik API Key

1. Visit [Freepik API Dashboard](https://www.freepik.com/developers/dashboard)
2. Sign up or log in to your Freepik account
3. Create a new API key for your project
4. Copy the API key

### 2. Configure Environment Variables

Add your Freepik API key to the `.env` file:

```env
FREEPIK_API_KEY=your_actual_freepik_api_key_here
```

Replace `your_actual_freepik_api_key_here` with the API key you obtained from Freepik.

### 3. API Limits and Pricing

- **Free Tier**: Limited requests per day
- **Paid Plans**: Higher limits and additional features
- Check the [Freepik API pricing](https://www.freepik.com/api) for current limits

## How It Works

### 1. Automatic Image Generation

When a user generates an alternate scenario:

1. The system extracts the `scene_visuals` from the Gemini AI response
2. A comprehensive prompt is constructed from:
   - `background_description`: Setting and environment details
   - `character_state`: Character appearance and expression
   - `scene_mood`: Lighting, colors, and atmosphere
3. The prompt is enhanced with quality modifiers
4. Freepik API generates a high-quality image
5. The image is displayed in the scenario interface

### 2. Prompt Construction

The system intelligently combines the scene visual elements into book illustration-focused prompts:

```javascript
// Example of how prompts are constructed
const prompt = `A detailed book illustration depicting ${character_state} in ${background_description}. The scene has ${scene_mood}. Style: professional book illustration, storybook art, detailed digital painting, rich colors, clear composition, literary artwork, fantasy book cover style, high quality, sharp details, artistic lighting, engaging visual storytelling`;
```

**Key improvements:**
- Explicitly mentions "book illustration" context
- Uses descriptive narrative structure
- Includes specific style keywords for better results
- Adds negative prompts to avoid common issues
- Adjusts styling parameters based on scene mood

### 3. Image Styling

The system automatically adjusts image styling based on the scene mood:

- **Dark/Mysterious**: Dark lighting, desaturated colors
- **Bright/Cheerful**: Bright lighting, vibrant colors
- **Dramatic**: Dramatic lighting, saturated colors
- **Default**: Natural lighting and colors

## Files Added/Modified

### New Files

1. **`app/utils/freepikService.js`**
   - Main service for Freepik API integration
   - Functions for generating images and constructing prompts

2. **`app/api/generate-image/route.js`**
   - Server-side API endpoint for secure Freepik API calls
   - Handles image generation requests

### Modified Files

1. **`app/components/AlternateScenarioDisplay.js`**
   - Added image generation functionality
   - Added loading states and error handling
   - Displays generated images

2. **`.env`**
   - Added Freepik API key configuration

## Usage

### For Users

1. Upload and analyze a PDF as usual
2. Navigate to the alternate scenario page
3. The system will automatically generate both text and images
4. Images appear above the scenario text with loading indicators

### For Developers

#### Generate a single image:

```javascript
import { generateSceneImage } from '../utils/freepikService';

const result = await generateSceneImage({
  background_description: "A medieval castle at sunset",
  character_state: "A knight in shining armor looking determined",
  scene_mood: "Epic, dramatic, golden hour lighting"
});

if (result.success) {
  const imageBase64 = result.data.base64;
  // Use the image
}
```

#### Generate multiple images:

```javascript
import { generateScenarioImages } from '../utils/freepikService';

const result = await generateScenarioImages(scenarioData);
```

## Error Handling

The system includes comprehensive error handling:

- **API Key Missing**: Clear error message for configuration issues
- **API Limits Exceeded**: Graceful degradation with error display
- **Network Issues**: Retry logic and user-friendly error messages
- **Invalid Responses**: Fallback handling for malformed API responses

## Customization

### Adjusting Image Style

Modify the styling in `app/api/generate-image/route.js`:

```javascript
const freepikPayload = {
  // ... other settings
  styling: {
    style: "digital-art", // Options: digital-art, anime, etc.
    effects: {
      color: "vibrant",     // pastel, vibrant, etc.
      lightning: "warm"     // warm, cold, dramatic, etc.
    }
  }
};
```

### Changing Image Dimensions

Modify the image size in the API route:

```javascript
image: {
  size: "landscape_16_9" // Options: square_1_1, landscape_16_9, portrait_9_16, etc.
}
```

## Testing

### Quick Test

Run the simple test script to verify your API key works:

```bash
node test-freepik-simple.js
```

This will generate a test image and save it as `test-generated-image-simple.png`.

### Full Test

Run the complete test with styling parameters:

```bash
node test-freepik.js
```

## Troubleshooting

### Common Issues

1. **"Freepik API key not configured"**
   - Ensure `FREEPIK_API_KEY` is set in your `.env` file
   - Restart your development server after adding the key

2. **"Your request parameters didn't validate" (styling errors)**
   - The system automatically falls back to minimal parameters if styling fails
   - Check the console for "Styling parameters failed, trying with minimal payload..."

3. **"API rate limit exceeded"**
   - Check your Freepik API usage in the dashboard
   - Consider upgrading your plan or implementing request queuing

4. **Images not generating**
   - Check browser console for error messages
   - Verify your API key is valid and active
   - Check network connectivity

### Debug Mode

Enable debug logging by adding console logs in the service files to track the image generation process.

## Future Enhancements

Potential improvements for the Freepik integration:

1. **Multiple Images**: Generate different images for each scenario screen
2. **Image Caching**: Store generated images to avoid regenerating
3. **Style Preferences**: Allow users to choose image styles
4. **Batch Generation**: Generate multiple variations of the same scene
5. **Image Editing**: Integration with Freepik's image editing APIs

## Support

For issues related to:
- **Freepik API**: Contact [Freepik Support](https://www.freepik.com/api#contact)
- **Integration Code**: Check the project's GitHub issues or documentation
