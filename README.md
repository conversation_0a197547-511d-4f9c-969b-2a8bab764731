# The Money Tales

A Next.js application for analyzing PDFs and generating alternate timeline fiction scenarios using AI.

## Features

- PDF processing and chunking
- Text embedding using Xenova TransformerJS
- Semantic search and analysis
- Alternate timeline fiction generation using Google's Gemini AI

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- A Google API key for Gemini AI
- Firebase project (optional, for authentication and storage)

### Environment Setup

1. Copy the `.env.local.example` file to `.env.local`:

```bash
cp .env.local.example .env.local
```

2. Fill in the required environment variables:
   - For Firebase configuration (optional)
   - For Google Gemini API (required for alternate scenario generation)

### Installation

Install the dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

### Running the Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## How It Works

### PDF Analysis

1. Upload a PDF document
2. The application extracts the text content and divides it into chunks
3. Embeddings are generated for each chunk using Xenova TransformerJS
4. Enter a prompt to search for relevant content within the PDF

### Alternate Scenario Generation

1. After analyzing a PDF, click on "Create Alternate Scenario"
2. Fill in the details about the book and the "what if" scenario
3. The application uses Google's Gemini AI to generate an alternate timeline fiction scenario
4. The system automatically generates visual images using Freepik AI based on the scenario descriptions
5. The generated scenario includes both text content and AI-generated images for immersive storytelling

## Technologies Used

- [Next.js](https://nextjs.org/) - React framework
- [Xenova TransformerJS](https://huggingface.co/Xenova) - Client-side embeddings and text analysis
- [Google Generative AI (Gemini)](https://ai.google.dev/) - Alternate scenario generation
- [Freepik API](https://www.freepik.com/api) - AI image generation for scenarios
- [PDF.js](https://mozilla.github.io/pdf.js/) - PDF processing
- [Firebase](https://firebase.google.com/) - Authentication and storage (optional)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
