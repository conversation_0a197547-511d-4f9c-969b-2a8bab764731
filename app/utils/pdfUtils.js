'use client';

/**
 * PDF.js utility functions with robust error handling
 */

let pdfJS = null;
let isInitialized = false;

/**
 * Initialize PDF.js with proper worker configuration
 */
export async function initializePDFJS() {
  if (isInitialized && pdfJS) {
    return pdfJS;
  }

  try {
    // Import PDF.js
    pdfJS = await import('pdfjs-dist/build/pdf');
    
    // Configure worker with multiple fallback options
    if (typeof window !== 'undefined') {
      // Try local worker first
      try {
        pdfJS.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';
        
        // Test if worker loads
        const testWorker = new Worker('/pdf.worker.min.mjs');
        testWorker.terminate();
        
        console.log('PDF.js worker loaded successfully from local path');
      } catch (workerError) {
        console.warn('Local worker failed, using CDN fallback:', workerError);
        
        // Fallback to CDN
        pdfJS.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfJS.version}/pdf.worker.min.mjs`;
      }
    }
    
    isInitialized = true;
    return pdfJS;
    
  } catch (error) {
    console.error('Failed to initialize PDF.js:', error);
    throw new Error('Failed to load PDF processing library. Please refresh the page and try again.');
  }
}

/**
 * Load a PDF document from a file
 * @param {File} file - The PDF file to load
 * @returns {Promise<Object>} PDF document object
 */
export async function loadPDFDocument(file) {
  const pdfLib = await initializePDFJS();
  
  try {
    // Convert file to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Create loading task
    const loadingTask = pdfLib.getDocument({
      data: arrayBuffer,
      verbosity: 0, // Reduce console output
      disableAutoFetch: false,
      disableStream: false
    });
    
    // Add timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('PDF loading timeout after 30 seconds')), 30000);
    });
    
    // Load with timeout
    const pdf = await Promise.race([loadingTask.promise, timeoutPromise]);
    
    console.log(`PDF loaded successfully: ${pdf.numPages} pages`);
    return pdf;
    
  } catch (error) {
    console.error('Error loading PDF:', error);
    
    if (error.message.includes('timeout')) {
      throw new Error('PDF loading timed out. The file might be too large or corrupted.');
    } else if (error.message.includes('Invalid PDF')) {
      throw new Error('Invalid PDF file. Please ensure the file is a valid PDF document.');
    } else if (error.message.includes('password')) {
      throw new Error('This PDF is password protected. Please use an unprotected PDF file.');
    } else {
      throw new Error(`Failed to load PDF: ${error.message}`);
    }
  }
}

/**
 * Extract text from a specific page
 * @param {Object} pdf - PDF document object
 * @param {number} pageNum - Page number (1-based)
 * @returns {Promise<string>} Extracted text
 */
export async function extractTextFromPage(pdf, pageNum) {
  try {
    const page = await pdf.getPage(pageNum);
    const textContent = await page.getTextContent();
    return textContent.items.map(item => item.str).join(' ');
  } catch (error) {
    console.error(`Error extracting text from page ${pageNum}:`, error);
    return '';
  }
}

/**
 * Extract text from a range of pages
 * @param {Object} pdf - PDF document object
 * @param {number} startPage - Start page (1-based)
 * @param {number} endPage - End page (1-based)
 * @returns {Promise<string>} Extracted text
 */
export async function extractTextFromPageRange(pdf, startPage, endPage) {
  const textPromises = [];
  for (let i = startPage; i <= endPage; i++) {
    textPromises.push(extractTextFromPage(pdf, i));
  }
  const pageTexts = await Promise.all(textPromises);
  return pageTexts.join('\n\n');
}

/**
 * Get PDF outline/bookmarks
 * @param {Object} pdf - PDF document object
 * @returns {Promise<Array>} Outline items
 */
export async function getPDFOutline(pdf) {
  try {
    const outline = await pdf.getOutline();
    return outline || [];
  } catch (error) {
    console.error('Error getting PDF outline:', error);
    return [];
  }
}

/**
 * Process outline items to extract page numbers
 * @param {Object} pdf - PDF document object
 * @param {Array} outlineItems - Outline items from PDF
 * @returns {Promise<Array>} Processed outline with page numbers
 */
export async function processOutlineItems(pdf, outlineItems) {
  const processedItems = [];
  
  for (const item of outlineItems) {
    const processedItem = { ...item };
    
    // Extract page number from destination
    if (item.dest) {
      try {
        let destRef = item.dest;
        if (typeof destRef === 'string') {
          destRef = await pdf.getDestination(destRef);
        }
        
        if (Array.isArray(destRef) && destRef.length > 0) {
          const pageRef = destRef[0];
          const pageNum = await pdf.getPageIndex(pageRef) + 1;
          processedItem.pageNumber = pageNum;
        }
      } catch (error) {
        console.error('Error extracting page number:', error);
      }
    }
    
    // Process nested items
    if (item.items && item.items.length > 0) {
      processedItem.items = await processOutlineItems(pdf, item.items);
    }
    
    processedItems.push(processedItem);
  }
  
  return processedItems;
}

/**
 * Create PDF chunks based on outline or default page ranges
 * @param {Object} pdf - PDF document object
 * @param {Array} outline - Processed outline items
 * @param {number} defaultChunkSize - Default chunk size if no outline
 * @returns {Promise<Array>} PDF chunks
 */
export async function createPDFChunks(pdf, outline, defaultChunkSize = 20) {
  const numPages = pdf.numPages;
  
  if (outline && outline.length > 0) {
    // Create chunks based on outline
    const chunks = [];
    const deepestSections = findDeepestSections(outline);
    
    for (let i = 0; i < deepestSections.length; i++) {
      const section = deepestSections[i];
      const nextSection = deepestSections[i + 1];
      
      const startPage = section.pageNumber;
      const endPage = nextSection ? nextSection.pageNumber - 1 : numPages;
      
      if (startPage && startPage <= numPages) {
        const content = await extractTextFromPageRange(pdf, startPage, endPage);
        
        chunks.push({
          id: `outline-chunk-${i + 1}`,
          title: section.title,
          path: section.path,
          startPage: startPage,
          endPage: endPage,
          pageCount: endPage - startPage + 1,
          isOutlineChunk: true,
          content: content
        });
      }
    }
    
    if (chunks.length > 0) {
      return chunks;
    }
  }
  
  // Create default chunks
  const chunks = [];
  for (let i = 1; i <= numPages; i += defaultChunkSize) {
    const startPage = i;
    const endPage = Math.min(i + defaultChunkSize - 1, numPages);
    
    const content = await extractTextFromPageRange(pdf, startPage, endPage);
    
    chunks.push({
      id: `default-chunk-${Math.floor(i / defaultChunkSize) + 1}`,
      title: `Pages ${startPage}-${endPage}`,
      path: [`Pages ${startPage}-${endPage}`],
      startPage: startPage,
      endPage: endPage,
      pageCount: endPage - startPage + 1,
      isDefaultChunk: true,
      content: content
    });
  }
  
  return chunks;
}

/**
 * Find deepest sections in outline for chunking
 * @param {Array} items - Outline items
 * @returns {Array} Deepest sections with page numbers
 */
function findDeepestSections(items, depth = 0, path = []) {
  const sections = [];
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const currentPath = [...path, item.title];
    
    if (item.items && item.items.length > 0) {
      sections.push(...findDeepestSections(item.items, depth + 1, currentPath));
    } else if (item.pageNumber) {
      sections.push({
        title: item.title,
        pageNumber: item.pageNumber,
        depth: depth,
        path: currentPath
      });
    }
  }
  
  return sections;
}
