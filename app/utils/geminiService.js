'use client';

// Note: This file uses the API route to call Gemini API
// We don't import the Google GenAI library directly in client code
// to avoid exposing API keys

/**
 * Generate an alternate timeline fiction scenario using Gemini
 * @param {Object} params - Parameters for the scenario generation
 * @param {string} params.bookTitle - The title of the book
 * @param {string} params.author - The author of the book
 * @param {string} params.changeLocation - The chapter or section where the change occurs
 * @param {string} params.whatIfPrompt - The user's "what if" prompt
 * @returns {Promise<Object>} The generated scenario in JSON format
 */
export async function generateAlternateScenario({
  bookTitle,
  author,
  changeLocation,
  whatIfPrompt
}) {
  try {
    // Call the API route instead of directly using the Gemini API
    const response = await fetch('/api/generate-scenario', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bookTitle,
        author,
        changeLocation,
        whatIfPrompt
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.error || `Server error: ${response.status}`
      };
    }

    return result;
  } catch (error) {
    console.error('Error generating alternate scenario:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

/**
 * Generate a complete story tree with branching scenarios
 * @param {Object} params - Parameters for the story tree generation
 * @param {string} params.bookTitle - The title of the book
 * @param {string} params.author - The author of the book
 * @param {string} params.changeLocation - The chapter or section where the change occurs
 * @param {string} params.whatIfPrompt - The user's "what if" prompt
 * @returns {Promise<Object>} The generated story tree with image and branching scenarios
 */
export async function generateStoryTree({
  bookTitle,
  author,
  changeLocation,
  whatIfPrompt
}) {
  try {
    // Call the API route for story tree generation
    const response = await fetch('/api/generate-story-tree', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bookTitle,
        author,
        changeLocation,
        whatIfPrompt
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.error || `Server error: ${response.status}`
      };
    }

    return result;
  } catch (error) {
    console.error('Error generating story tree:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

/**
 * Process the analysis results and generate an alternate scenario
 * @param {Object} analysisResult - The analysis result from the embedding analysis
 * @param {string} userPrompt - The user's prompt
 * @returns {Promise<Object>} The generated scenario
 */
export async function processAnalysisAndGenerateScenario(analysisResult, userPrompt) {
  try {
    // Extract relevant information from the analysis result
    // This is a simplified example - you would need to adapt this to your actual data structure
    const topChunk = analysisResult[0]; // Assuming the first result is the most relevant

    // Extract book information from the chunk
    // This is hypothetical - you'll need to adapt to your actual data structure
    const bookTitle = topChunk.metadata?.title || 'Unknown Book';
    const author = topChunk.metadata?.author || 'Unknown Author';
    const changeLocation = topChunk.metadata?.section || 'Unknown Section';

    // Use the chunk content as the original event
    const originalEvent = topChunk.content || '';

    // Generate the alternate scenario
    return await generateAlternateScenario({
      bookTitle,
      author,
      changeLocation,
      originalEvent,
      whatIfPrompt: userPrompt
    });
  } catch (error) {
    console.error('Error processing analysis and generating scenario:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}
