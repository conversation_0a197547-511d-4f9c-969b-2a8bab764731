'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { signOut } from 'firebase/auth';
import { auth } from '../../lib/firebase';
import { generateStoryTree } from '../utils/geminiService';
import { extractBookInformation } from '../utils/embeddingAnalysis';

export default function AlternateScenarioPage() {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(true);
  const [storyTree, setStoryTree] = useState(null);
  const [currentNode, setCurrentNode] = useState(null);
  const [currentLevel, setCurrentLevel] = useState(0); // 0=root, 1=level2, 2=level3
  const [generatedImage, setGeneratedImage] = useState(null);
  const [error, setError] = useState('');
  // We store the analysis results but don't directly use them in rendering
  const [, setAnalysisResults] = useState(null);
  const [userPrompt, setUserPrompt] = useState('');
  const [bookInfo, setBookInfo] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    originalEvent: ''
  });

  // Check if we're in the browser
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Function to generate the scenario
  const generateScenario = useCallback(async (bookData, whatIfPrompt) => {
    try {
      // Ensure we have the required data
      if (!bookData.bookTitle || !bookData.author || !whatIfPrompt) {
        setError('Missing required information to generate scenario.');
        setIsGenerating(false);
        return;
      }

      // Generate the story tree
      const result = await generateStoryTree({
        bookTitle: bookData.bookTitle,
        author: bookData.author,
        changeLocation: bookData.changeLocation,
        whatIfPrompt: whatIfPrompt
      });

      if (result.success) {
        setStoryTree(result);
        setCurrentNode(result.root);
        setCurrentLevel(0);
        // Generate image for the root scenario
        if (result.root.imagePrompt) {
          generateScenarioImage(result.root.imagePrompt);
        }
      } else {
        setError(result.error || 'Failed to generate story tree');
      }
    } catch (error) {
      console.error('Error generating scenario:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Load data from localStorage and generate scenario automatically
  useEffect(() => {
    if (!isClient) return;

    async function loadDataAndGenerateScenario() {
      try {
        // Load analysis results and user prompt from localStorage
        const savedResults = localStorage.getItem('analysisResults');
        const savedPrompt = localStorage.getItem('userPrompt');

        if (!savedResults || !savedPrompt) {
          setError('No analysis results or prompt found. Please analyze a PDF first.');
          setIsGenerating(false);
          return;
        }

        const parsedResults = JSON.parse(savedResults);
        setAnalysisResults(parsedResults);
        setUserPrompt(savedPrompt);

        // Extract book information from analysis results
        const extractedBookInfo = extractBookInformation(parsedResults);
        setBookInfo(extractedBookInfo);

        // Generate the alternate scenario automatically
        await generateScenario(extractedBookInfo, savedPrompt);
      } catch (error) {
        console.error('Error loading data and generating scenario:', error);
        setError('Error loading data: ' + (error.message || 'Unknown error'));
        setIsGenerating(false);
      }
    }

    loadDataAndGenerateScenario();
  }, [isClient, generateScenario]);

  const generateScenarioImage = async (imagePrompt) => {
    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: imagePrompt
        }),
      });

      const result = await response.json();
      if (result.success && result.data && result.data.base64) {
        // Create a data URL from the base64 image
        const imageUrl = `data:image/png;base64,${result.data.base64}`;
        setGeneratedImage(imageUrl);
      }
    } catch (error) {
      console.error('Error generating image:', error);
    }
  };

  const handleChoiceClick = (choiceId) => {
    if (currentLevel === 0 && storyTree?.level2?.[choiceId]) {
      // Moving from root to level 2
      setCurrentNode(storyTree.level2[choiceId]);
      setCurrentLevel(1);
    } else if (currentLevel === 1 && storyTree?.level3?.[choiceId]) {
      // Moving from level 2 to level 3
      setCurrentNode(storyTree.level3[choiceId]);
      setCurrentLevel(2);
    }
  };

  const handleLogout = async () => {
    try {
      // Call logout API to clear cookies
      await fetch('/api/auth/logout', {
        method: 'POST',
      });

      // Sign out from Firebase
      await signOut(auth);

      // Redirect to main page
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[#282A2F] text-white">
      {/* Header */}
      <header className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">Interactive Story Tree</h1>
          <div className="flex space-x-3">
            <Link
              href="/"
              className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              Back to Analysis
            </Link>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        {/* Loading State */}
        {isGenerating && (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-lg">Generating your interactive story tree...</p>
              <p className="text-sm text-gray-400 mt-2">This may take a moment as we create multiple story branches</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-900/50 border border-red-700 p-6 rounded-lg text-center">
            <h2 className="text-xl font-bold mb-2">Error</h2>
            <p className="mb-4">{error}</p>
            <Link
              href="/"
              className="px-6 py-3 bg-primary-green text-white rounded-md hover:bg-primary-green-dark transition-colors"
            >
              Return to PDF Analysis
            </Link>
          </div>
        )}

        {/* Story Tree Display */}
        {!isGenerating && !error && currentNode && (
          <div className="grid grid-cols-3 gap-6 h-[calc(100vh-200px)]">
            {/* Left Section - Image (2/3 width) */}
            <div className="col-span-2 bg-gray-800 rounded-lg overflow-hidden">
              <div className="h-full flex items-center justify-center">
                {generatedImage ? (
                  <Image
                    src={generatedImage}
                    alt="Scenario illustration"
                    width={800}
                    height={600}
                    className="max-w-full max-h-full object-contain rounded-lg"
                  />
                ) : (
                  <div className="text-center text-gray-400">
                    <div className="w-24 h-24 bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center">
                      <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p>Generating scenario image...</p>
                  </div>
                )}
              </div>
            </div>

            {/* Right Section - Story Content (1/3 width) */}
            <div className="col-span-1 bg-gray-800 rounded-lg p-6 overflow-y-auto">
              {/* Book Information */}
              <div className="mb-6 pb-4 border-b border-gray-700">
                <h3 className="text-lg font-semibold mb-2">{storyTree?.bookInfo?.title}</h3>
                <p className="text-sm text-gray-400">by {storyTree?.bookInfo?.author}</p>
                <p className="text-xs text-gray-500 mt-1">Change at: {storyTree?.bookInfo?.changeLocation}</p>
              </div>

              {/* Current Scenario */}
              <div className="mb-6">
                <h2 className="text-xl font-bold mb-3">{currentNode.title}</h2>
                <div className="text-sm leading-relaxed text-gray-200 whitespace-pre-wrap">
                  {currentNode.text}
                </div>
              </div>

              {/* Choice Buttons */}
              {currentNode.choices && currentNode.choices.length > 0 && !currentNode.isEnding && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold mb-3">What happens next?</h3>
                  {currentNode.choices.map((choice, index) => {
                    const isAvailable = currentLevel === 0
                      ? storyTree?.level2?.[choice.id]
                      : currentLevel === 1
                        ? storyTree?.level3?.[choice.id]
                        : false;

                    return (
                      <button
                        key={choice.id}
                        onClick={() => handleChoiceClick(choice.id)}
                        className="w-full text-left p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors border border-gray-600 hover:border-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={!isAvailable}
                      >
                        <div className="font-medium text-white mb-1">
                          {index + 1}. {choice.text}
                        </div>
                        <div className="text-xs text-gray-400">
                          {choice.preview}
                        </div>
                      </button>
                    );
                  })}
                </div>
              )}

              {/* Story Ending Indicator */}
              {currentNode.isEnding && (
                <div className="mt-6 p-4 bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg border border-purple-500/30">
                  <div className="text-center">
                    <div className="text-2xl mb-2">📖</div>
                    <h3 className="text-lg font-semibold text-purple-300 mb-2">Story Complete</h3>
                    <p className="text-sm text-gray-300">
                      You&apos;ve reached the end of this alternate timeline. The choices you made have shaped a unique conclusion to the story.
                    </p>
                  </div>
                </div>
              )}

              {/* Back to Root Button (for level 2+ scenarios) */}
              {currentNode.id !== 'root' && (
                <div className="mt-6 pt-4 border-t border-gray-700">
                  <button
                    onClick={() => {
                      setCurrentNode(storyTree.root);
                      setCurrentLevel(0);
                    }}
                    className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    ← Back to Beginning
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
