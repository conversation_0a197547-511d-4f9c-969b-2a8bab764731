'use client';

import { useState } from 'react';
import Image from 'next/image';
import { 
  ChevronRight, 
  MessageSquare, 
  Clock, 
  User, 
  Settings, 
  Globe, 
  Type, 
  Shield,
  Eye,
  EyeOff,
  Camera
} from 'lucide-react';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('profile');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState({
    chatHistory: false,
    settings: false
  });
  
  const [formData, setFormData] = useState({
    fullname: '<PERSON>',
    email: '<EMAIL>',
    password: '',
    confirmPassword: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleUpdate = (e) => {
    e.preventDefault();
    // TODO: Implement update logic
    console.log('Update profile:', formData);
  };

  const toggleSidebarSection = (section) => {
    setSidebarExpanded(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const tabs = [
    { id: 'profile', label: 'Profile Details' },
    { id: 'preferences', label: 'Preferences' },
    { id: 'usage', label: 'Usage' },
    { id: 'billing', label: 'Plan and Billing' }
  ];

  return (
    <div className="flex h-screen bg-[#2C2D30]">
      {/* Sidebar */}
      <div className="w-48 bg-[#2C2D30] border-r border-[#3E3F43] flex flex-col">
        {/* Logo */}
        <div className="p-4 flex items-center justify-center">
          <div className="w-8 h-8 bg-gray-400 rounded flex items-center justify-center">
            <ChevronRight className="w-5 h-5 text-gray-700" />
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 space-y-1">
          {/* Chat */}
          <div className="bg-[#7C3AED] rounded-lg p-2 flex items-center space-x-2 cursor-pointer">
            <MessageSquare className="w-4 h-4 text-white" />
            <span className="text-sm text-white">Chat</span>
          </div>

          {/* Chat History */}
          <div className="mt-2">
            <button
              onClick={() => toggleSidebarSection('chatHistory')}
              className="w-full flex items-center justify-between p-2 hover:bg-[#3E3F43] rounded-lg transition-colors"
            >
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-300">Chat History</span>
              </div>
              <ChevronRight 
                className={`w-4 h-4 text-gray-400 transition-transform ${
                  sidebarExpanded.chatHistory ? 'rotate-90' : ''
                }`} 
              />
            </button>
          </div>

          {/* AI Personalities */}
          <div className="flex items-center space-x-2 p-2 hover:bg-[#3E3F43] rounded-lg cursor-pointer transition-colors">
            <User className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-300">AI Personalities</span>
          </div>

          {/* Settings */}
          <div className="mt-2">
            <button
              onClick={() => toggleSidebarSection('settings')}
              className="w-full flex items-center justify-between p-2 hover:bg-[#3E3F43] rounded-lg transition-colors"
            >
              <div className="flex items-center space-x-2">
                <Settings className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-300">Settings</span>
              </div>
              <ChevronRight 
                className={`w-4 h-4 text-gray-400 transition-transform ${
                  sidebarExpanded.settings ? 'rotate-90' : ''
                }`} 
              />
            </button>
            
            {sidebarExpanded.settings && (
              <div className="ml-6 mt-1 space-y-1">
                <div className="flex items-center space-x-2 p-2 hover:bg-[#3E3F43] rounded-lg cursor-pointer transition-colors">
                  <Globe className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-300">Tone</span>
                </div>
                <div className="flex items-center space-x-2 p-2 hover:bg-[#3E3F43] rounded-lg cursor-pointer transition-colors">
                  <Type className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-300">Language</span>
                </div>
                <div className="flex items-center space-x-2 p-2 hover:bg-[#3E3F43] rounded-lg cursor-pointer transition-colors">
                  <Shield className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-300">Formality Level</span>
                </div>
              </div>
            )}
          </div>
        </nav>

        {/* Profile Icon */}
        <div className="p-4 border-t border-[#3E3F43]">
          <div className="flex items-center justify-center">
            <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-600">
              <Image
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop"
                alt="Profile"
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 bg-[#1E1F22]">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-[#3E3F43]">
          <div className="flex items-center space-x-4">
            <Shield className="w-6 h-6 text-gray-400" />
            <Globe className="w-6 h-6 text-gray-400" />
          </div>
          <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-600">
            <Image
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop"
              alt="Profile"
              width={40}
              height={40}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Content Area */}
        <div className="p-6">
          {/* Tabs */}
          <div className="flex space-x-8 border-b border-[#3E3F43] mb-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`pb-4 text-sm font-medium transition-colors relative ${
                  activeTab === tab.id
                    ? 'text-white'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                {tab.label}
                {activeTab === tab.id && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#7C3AED]" />
                )}
              </button>
            ))}
          </div>

          {/* Profile Content */}
          {activeTab === 'profile' && (
            <div className="max-w-2xl">
              {/* Profile Picture Section */}
              <div className="mb-8">
                <div className="relative inline-block">
                  <div className="w-24 h-24 rounded-lg overflow-hidden bg-gray-700">
                    <Image
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop"
                      alt="Profile"
                      width={96}
                      height={96}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <button className="absolute bottom-1 right-1 w-7 h-7 bg-white rounded-md flex items-center justify-center shadow-lg hover:bg-gray-100 transition-colors">
                    <Camera className="w-4 h-4 text-gray-700" />
                  </button>
                </div>
              </div>

              {/* Personal Information */}
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-white mb-6">Personal Information</h2>
                
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">
                      Your fullname*
                    </label>
                    <input
                      type="text"
                      name="fullname"
                      value={formData.fullname}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2.5 bg-[#2C2D30] border border-[#3E3F43] rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#7C3AED] transition-colors"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">
                      Your email*
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2.5 bg-[#2C2D30] border border-[#3E3F43] rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#7C3AED] transition-colors"
                    />
                  </div>
                </div>
              </div>

              {/* Password Section */}
              <div className="mb-8">
                <h2 className="text-lg font-semibold text-white mb-6">Password</h2>
                
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">
                      Password*
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        placeholder="••••••••"
                        className="w-full px-4 py-2.5 bg-[#2C2D30] border border-[#3E3F43] rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#7C3AED] transition-colors pr-10"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                      >
                        {showPassword ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm text-gray-400 mb-2">
                      Confirm Password*
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        placeholder="••••••••"
                        className="w-full px-4 py-2.5 bg-[#2C2D30] border border-[#3E3F43] rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#7C3AED] transition-colors pr-10"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Update Button */}
              <button
                onClick={handleUpdate}
                className="px-6 py-2.5 bg-[#7C3AED] text-white rounded-lg font-medium hover:bg-[#6D28D9] transition-colors flex items-center space-x-2"
              >
                <span>Update</span>
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          )}

          {/* Other tabs content */}
          {activeTab === 'preferences' && (
            <div className="text-gray-400">
              <p>Preferences content goes here...</p>
            </div>
          )}

          {activeTab === 'usage' && (
            <div className="text-gray-400">
              <p>Usage content goes here...</p>
            </div>
          )}

          {activeTab === 'billing' && (
            <div className="text-gray-400">
              <p>Plan and Billing content goes here...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}