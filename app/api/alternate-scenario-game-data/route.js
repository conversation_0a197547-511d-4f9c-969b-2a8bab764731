import { NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';

// Initialize the Gemini API client
const apiKey = process.env.GOOGLE_API_KEY;
const genAI = new GoogleGenAI({ apiKey });

// Helper function to fix common JSON issues (reused from generate-story-tree)
function fixJsonString(jsonStr) {
  let fixed = jsonStr;
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');
  fixed = fixed.replace(/"\s*\n\s*"/g, '",\n"');
  fixed = fixed.replace(/}(\s*)"([^"]+)":/g, '},\n"$2":');
  fixed = fixed.replace(/}(\s*)"/g, '},\n"');
  fixed = fixed.replace(/"(\s*)"([^"]+)":/g, '",\n"$2":');
  fixed = fixed.replace(/:\s*"([^"\\]*)\\?([^"\\]*)\\?([^"]*)"(\s*[,}\]])/g, (_, p1, p2, p3, p4) => {
    const content = (p1 + p2 + p3).replace(/"/g, '\\"');
    return `: "${content}"${p4}`;
  });
  fixed = fixed.replace(/:\s*"([^"]*)\n([^"]*)"(\s*[,}])/g, ': "$1\\n$2"$3');
  fixed = fixed.replace(/:\s*'([^']*)'/g, ': "$1"');
  fixed = fixed.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');
  fixed = fixed.replace(/:\s*"(true|false|null)"/g, ': $1');
  return fixed;
}

// Helper function to safely parse JSON from AI responses
function safeJsonParse(text, context = '') {
  try {
    let cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
    cleanedText = cleanedText.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');
    cleanedText = cleanedText.replace(/[\u2018\u2019]/g, "'");
    cleanedText = cleanedText.replace(/[\u201C\u201D]/g, '"');
    cleanedText = cleanedText.replace(/\u2026/g, '...');

    const jsonObjectMatch = cleanedText.match(/(\{[\s\S]*\})/);
    if (jsonObjectMatch && jsonObjectMatch[1]) {
      cleanedText = jsonObjectMatch[1];
    }

    cleanedText = fixJsonString(cleanedText);
    return JSON.parse(cleanedText);
  } catch (error) {
    console.error(`JSON parsing error in ${context}:`, error);
    throw error;
  }
}

// Transform story tree data into Godot-compatible quiz format
function transformStoryTreeToQuizFormat(storyTree) {
  const questions = [];
  
  // Add root scenario as first question
  if (storyTree.root) {
    questions.push({
      question: storyTree.root.text,
      title: storyTree.root.title,
      options: storyTree.root.choices ? storyTree.root.choices.map(choice => choice.text) : [],
      correct_answer: -1, // No correct answer for story choices
      choice_ids: storyTree.root.choices ? storyTree.root.choices.map(choice => choice.id) : [],
      level: 0,
      node_id: storyTree.root.id
    });
  }

  // Add level 2 scenarios
  if (storyTree.level2) {
    Object.values(storyTree.level2).forEach(scenario => {
      questions.push({
        question: scenario.text,
        title: scenario.title,
        options: scenario.choices ? scenario.choices.map(choice => choice.text) : [],
        correct_answer: -1,
        choice_ids: scenario.choices ? scenario.choices.map(choice => choice.id) : [],
        level: 1,
        node_id: scenario.id,
        is_ending: scenario.isEnding || false
      });
    });
  }

  // Add level 3 scenarios (endings)
  if (storyTree.level3) {
    Object.values(storyTree.level3).forEach(scenario => {
      questions.push({
        question: scenario.text,
        title: scenario.title,
        options: [], // No choices for endings
        correct_answer: -1,
        choice_ids: [],
        level: 2,
        node_id: scenario.id,
        is_ending: true
      });
    });
  }

  return {
    book_info: storyTree.bookInfo || {},
    story_tree: storyTree,
    questions: questions
  };
}

export async function POST(request) {
  try {
    const { bookTitle, author, changeLocation, whatIfPrompt } = await request.json();

    if (!bookTitle || !author || !changeLocation || !whatIfPrompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    const model = 'gemini-2.0-flash-001';

    // Generate the root scenario (simplified version for game)
    const rootPrompt = `
You are a creative fiction writer specializing in alternate timeline stories. Based on the book "${bookTitle}" by ${author}, create an engaging alternate scenario starting from ${changeLocation}.

User's "What If" scenario: ${whatIfPrompt}

Generate a JSON response with the following structure:
{
  "rootScenario": {
    "id": "root",
    "title": "Brief compelling title for this scenario",
    "text": "A complete, engaging narrative of 200-300 words describing the alternate scenario. Make it vivid and immersive.",
    "choices": [
      {
        "id": "choice1",
        "text": "First choice option (20-30 words)"
      },
      {
        "id": "choice2", 
        "text": "Second choice option (20-30 words)"
      },
      {
        "id": "choice3",
        "text": "Third choice option (20-30 words)"
      }
    ]
  }
}

Make sure each choice leads to meaningfully different story directions. The scenario should feel authentic to the original book's tone and style.
`;

    const rootResult = await genAI.models.generateContent({
      model,
      contents: [
        { role: 'user', parts: [{ text: rootPrompt }] }
      ],
      config: {
        temperature: 0.7,
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 1024,
      },
    });

    let rootText = '';
    if (rootResult.candidates && rootResult.candidates.length > 0) {
      const candidate = rootResult.candidates[0];
      if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
        rootText = candidate.content.parts[0].text || '';
      }
    }

    let rootScenario;
    try {
      rootScenario = safeJsonParse(rootText, 'Root scenario');
    } catch (parseError) {
      console.error('Error parsing root scenario JSON:', parseError);
      return NextResponse.json(
        { error: 'Failed to parse root scenario response' },
        { status: 500 }
      );
    }

    // Generate Level 2 scenarios for each choice
    const level2Scenarios = {};

    for (const choice of rootScenario.rootScenario.choices) {
      const level2Prompt = `
Continue the alternate timeline story from "${bookTitle}" by ${author}.

Previous scenario: ${rootScenario.rootScenario.text}

The reader chose: "${choice.text}"

Generate a JSON response with this structure:
{
  "scenario": {
    "id": "${choice.id}",
    "title": "Brief compelling title for this continuation",
    "text": "A complete narrative of 200-300 words continuing from the chosen path. Show the consequences of the choice and develop the story further.",
    "choices": [
      {
        "id": "${choice.id}_1",
        "text": "First choice option (20-30 words)"
      },
      {
        "id": "${choice.id}_2", 
        "text": "Second choice option (20-30 words)"
      }
    ]
  }
}

Maintain consistency with the original book's tone and the established alternate timeline.
`;

      try {
        const level2Result = await genAI.models.generateContent({
          model,
          contents: [
            { role: 'user', parts: [{ text: level2Prompt }] }
          ],
          config: {
            temperature: 0.7,
            topP: 0.9,
            topK: 40,
            maxOutputTokens: 1024,
          },
        });

        let level2Text = '';
        if (level2Result.candidates && level2Result.candidates.length > 0) {
          const candidate = level2Result.candidates[0];
          if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
            level2Text = candidate.content.parts[0].text || '';
          }
        }

        const level2Data = safeJsonParse(level2Text, `Level 2 scenario for ${choice.id}`);
        level2Scenarios[choice.id] = level2Data.scenario;
      } catch (error) {
        console.error(`Error generating level 2 scenario for ${choice.id}:`, error);
        // Create a fallback scenario
        level2Scenarios[choice.id] = {
          id: choice.id,
          title: "Story Continues...",
          text: "The story continues from your choice. More adventures await as the alternate timeline unfolds with unexpected consequences and new possibilities.",
          choices: [
            { id: `${choice.id}_1`, text: "Continue the adventure" },
            { id: `${choice.id}_2`, text: "Take a different approach" }
          ]
        };
      }
    }

    // Generate Level 3 scenarios (endings) for each level 2 choice
    const level3Scenarios = {};

    for (const [parentId, parentScenario] of Object.entries(level2Scenarios)) {
      if (parentScenario.choices) {
        for (const level2Choice of parentScenario.choices) {
          const level3Prompt = `
Continue the alternate timeline story from "${bookTitle}" by ${author}.

Previous scenario: ${parentScenario.text}

The reader chose: "${level2Choice.text}"

Generate a JSON response with this structure:
{
  "scenario": {
    "id": "${level2Choice.id}",
    "title": "Brief compelling title for this final chapter",
    "text": "A complete narrative of 250-350 words bringing this story branch to a satisfying conclusion. Show the ultimate consequences of all the choices made and provide closure to this alternate timeline.",
    "isEnding": true
  }
}

This should be a concluding chapter that wraps up this particular story branch. Maintain consistency with the original book's tone and the established alternate timeline.
`;

          try {
            const level3Result = await genAI.models.generateContent({
              model,
              contents: [
                { role: 'user', parts: [{ text: level3Prompt }] }
              ],
              config: {
                temperature: 0.7,
                topP: 0.9,
                topK: 40,
                maxOutputTokens: 1024,
              },
            });

            let level3Text = '';
            if (level3Result.candidates && level3Result.candidates.length > 0) {
              const candidate = level3Result.candidates[0];
              if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
                level3Text = candidate.content.parts[0].text || '';
              }
            }

            const level3Data = safeJsonParse(level3Text, `Level 3 scenario for ${level2Choice.id}`);
            level3Scenarios[level2Choice.id] = level3Data.scenario;
          } catch (error) {
            console.error(`Error generating level 3 scenario for ${level2Choice.id}:`, error);
            // Create a fallback ending scenario
            level3Scenarios[level2Choice.id] = {
              id: level2Choice.id,
              title: "The Story Concludes",
              text: "The alternate timeline reaches its conclusion. The choices made have led to this moment, where the consequences of change ripple through the narrative, creating a new ending to a familiar tale.",
              isEnding: true
            };
          }
        }
      }
    }

    const storyTree = {
      success: true,
      root: rootScenario.rootScenario,
      level2: level2Scenarios,
      level3: level3Scenarios,
      bookInfo: {
        title: bookTitle,
        author: author,
        changeLocation: changeLocation,
        whatIfPrompt: whatIfPrompt
      }
    };

    // Transform to Godot-compatible format
    const gameData = transformStoryTreeToQuizFormat(storyTree);

    return NextResponse.json(gameData);

  } catch (error) {
    console.error('Error generating alternate scenario game data:', error);
    return NextResponse.json(
      { error: 'Failed to generate game data' },
      { status: 500 }
    );
  }
}
