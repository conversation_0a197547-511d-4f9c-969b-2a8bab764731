import { NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { firebaseAdminApp } from '@/lib/firebase-admin';
/**
 * @route   POST /api/auth/save
 * @desc    Verifies Firebase ID token and saves user details to Firestore.
 * @access  Public
 * 
 * @requestBody
 * {
 *   "idToken": "string",         // Firebase Authentication ID token
 *   "provider": "string",        // Authentication provider (e.g., "google", "facebook")
 *   "displayName": "string"      // Optional: User's display name
 * }
 * 
 * @response
 * Success: 
 * {
 *   "success": true,
 *   "user": {
 *     "uid": "string",
 *     "email": "string",
 *     "displayName": "string",
 *     "photoURL": "string",
 *     "provider": "string",
 *     "role": "student",
 *     "isBlocked": false,
 *     "isDeleted": false,
 *     "createdAt": "ISO8601 string",
 *     "lastLogin": "ISO8601 string",
 *     "profileComplete": false,
 *     "progress": {
 *       "currentLevel": 1,
 *       "xp": 0,
 *       "achievementIds": []
 *     }
 *   }
 * }
 * 
 * Error:
 * {
 *   "success": false,
 *   "error": "Error message"
 * }
 */


export async function POST(request) {
  try {
    const { idToken, displayName } = await request.json();
    const auth = getAuth(firebaseAdminApp);
    const db = getFirestore(firebaseAdminApp);

    // Verify ID token
    const decodedToken = await auth.verifyIdToken(idToken);
    const uid = decodedToken.uid;

    // Prepare user data
    const userData = {
      uid,
      email: decodedToken.email || null,
      displayName: displayName || decodedToken.name || null,
      photoURL: decodedToken.picture || null,
      role: 'student',
      isBlocked: false,
      isDeleted: false,
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      profileComplete: false,
      progress: {
        currentLevel: 1,
        xp: 0,
        achievementIds: []
      }
    };

    // Save user data to Firestore
    await db.collection('users').doc(uid).set(userData, { merge: true });

    return NextResponse.json({ success: true, user: userData });

  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}