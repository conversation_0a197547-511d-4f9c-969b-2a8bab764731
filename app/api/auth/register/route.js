import { NextResponse } from 'next/server';
import { auth } from '@/lib/firebase';
import { createUserWithEmailAndPassword, GoogleAuthProvider, signInWithPopup } from 'firebase/auth';

export async function POST(request) {
  const { email, password, provider, username, phone, fname, lname } = await request.json();

  try {
    if (provider === 'google') {
      const googleProvider = new GoogleAuthProvider();
      const userCredential = await signInWithPopup(auth, googleProvider);
      return NextResponse.json({ success: true, user: userCredential.user });
    }

    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    await updateProfile(user, {
      displayName: `${fname} ${lname}`,
      phoneNumber: phoneNumber,
    });

    return Response.json({ success: true, user });
  } catch (error) {
    return NextResponse.json({ success: false, error: error.message });
  }
}