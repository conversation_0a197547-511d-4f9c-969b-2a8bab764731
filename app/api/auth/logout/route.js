import { NextResponse } from 'next/server';
import { auth } from '@/lib/firebase';

export async function POST() {
  try {
    await auth.signOut();
    
    // Create response with success message
    const response = NextResponse.json({ 
      success: true, 
      message: 'Logged out successfully' 
    });
    
    
    return response;
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}