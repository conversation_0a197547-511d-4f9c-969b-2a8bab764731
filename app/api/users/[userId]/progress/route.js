import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';

// Get user progress across all courses
export async function GET(request, { params }) {
  try {
    const { userId } = params;

    // Get user progress
    const progressQuery = query(
      collection(db, 'userProgress'),
      where('userId', '==', userId)
    );
    
    const progressSnapshot = await getDocs(progressQuery);
    const progress = progressSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    // Get user achievements
    const userDoc = await getDoc(doc(db, 'users', userId));
    const achievementIds = userDoc.data()?.progress?.achievementIds || [];

    const achievements = [];
    for (const achievementId of achievementIds) {
      const achievementDoc = await getDoc(doc(db, 'achievements', achievementId));
      if (achievementDoc.exists()) {
        achievements.push({
          id: achievementDoc.id,
          ...achievementDoc.data()
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        progress,
        achievements,
        currentLevel: userDoc.data()?.progress?.currentLevel || 1,
        xp: userDoc.data()?.progress?.xp || 0
      }
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}