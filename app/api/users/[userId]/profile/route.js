import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { auth } from '@/lib/firebase';

// Get user settings
export async function GET() {
  try {
    // Get the current logged-in user
    const user = auth.currentUser;
    
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    // Get the user document from Firestore
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    
    if (!userDoc.exists()) {
      return NextResponse.json({ 
        success: false, 
        error: 'User not found' 
      }, { status: 404 });
    }

    const userData = userDoc.data();
    
    // Return user profile data
    return NextResponse.json({
      success: true,
      displayName: userData.displayName || user.displayName || 'User',
      email: userData.email || user.email,
      photoURL: userData.photoURL || user.photoURL,
      uid: user.uid
    });
    
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

// Update user settings
export async function PUT(request, { params }) {
  try {
    const { userId } = params;
    const updates = await request.json();

    const allowedSettings = [
      'notifications',
      'language',
      'theme'
    ];

    // Filter out non-allowed settings
    const filteredSettings = Object.keys(updates)
      .filter(key => allowedSettings.includes(key))
      .reduce((obj, key) => {
        obj[key] = updates[key];
        return obj;
      }, {});

    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      settings: filteredSettings
    });

    const updatedDoc = await getDoc(userRef);
    return NextResponse.json({ 
      success: true, 
      settings: updatedDoc.data().settings 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}