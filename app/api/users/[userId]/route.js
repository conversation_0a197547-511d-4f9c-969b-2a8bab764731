import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';

// Get user details
export async function GET(request, { params }) {
  try {
    const { userId } = params;
    
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      return NextResponse.json({ 
        success: false, 
        error: 'User not found' 
      }, { status: 404 });
    }

    const userData = userDoc.data();
    return NextResponse.json({ 
      success: true, 
      user: userData 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

// Update user profile
export async function PUT(request, { params }) {
  try {
    const { userId } = params;
    const updates = await request.json();

    // Fields that can be updated
    const allowedUpdates = [
      'displayName',
      'phone',
      'photoURL',
      'profileComplete',
      'settings',
      'profile'
    ];

    // Filter out non-allowed updates
    const filteredUpdates = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj, key) => {
        obj[key] = updates[key];
        return obj;
      }, {});

    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      ...filteredUpdates,
      updatedAt: new Date().toISOString()
    });

    const updatedDoc = await getDoc(userRef);
    return NextResponse.json({ 
      success: true, 
      user: updatedDoc.data() 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}