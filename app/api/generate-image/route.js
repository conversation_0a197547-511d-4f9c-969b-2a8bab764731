import { NextResponse } from 'next/server';

/**
 * Generate an image using Freepik API
 * @route POST /api/generate-image
 */
export async function POST(request) {
  try {
    // Get request body
    const { prompt, sceneVisuals } = await request.json();

    // Validate required fields
    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Missing prompt' },
        { status: 400 }
      );
    }

    // Get Freepik API key
    const apiKey = process.env.FREEPIK_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'Freepik API key not configured' },
        { status: 500 }
      );
    }

    // Prepare the request payload for Freepik API
    const freepikPayload = {
      prompt: prompt,
      negative_prompt: "low quality, blurry, distorted, ugly, bad anatomy, watermark, text, signature, unclear faces, messy composition, poor lighting",
      guidance_scale: 8.0, // Slightly higher for better adherence to prompt
      num_images: 1,
      image: {
        size: "landscape_16_9" // Good for scenario images
      },
      styling: {
        style: "digital-art", // Best for book illustrations
        effects: {
          color: "vibrant", // Rich colors for engaging book illustrations
          lightning: "dramatic" // Better lighting for storytelling
        }
      },
      filter_nsfw: true
    };

    // Adjust styling based on scene mood if available
    if (sceneVisuals?.scene_mood) {
      const mood = sceneVisuals.scene_mood.toLowerCase();

      // Adjust lighting based on mood
      if (mood.includes('dark') || mood.includes('mysterious') || mood.includes('ominous')) {
        freepikPayload.styling.effects.lightning = "cold";
      } else if (mood.includes('warm') || mood.includes('cozy') || mood.includes('peaceful')) {
        freepikPayload.styling.effects.lightning = "warm";
      } else if (mood.includes('dramatic') || mood.includes('intense') || mood.includes('epic')) {
        freepikPayload.styling.effects.lightning = "dramatic";
      }

      // Adjust colors based on mood
      if (mood.includes('soft') || mood.includes('gentle') || mood.includes('peaceful')) {
        freepikPayload.styling.effects.color = "pastel";
      } else {
        freepikPayload.styling.effects.color = "vibrant";
      }
    }

    console.log('Generating image with Freepik API...');
    console.log('Prompt:', prompt);

    // Call Freepik API
    let freepikResponse = await fetch('https://api.freepik.com/v1/ai/text-to-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-freepik-api-key': apiKey,
      },
      body: JSON.stringify(freepikPayload),
    });

    let freepikData = await freepikResponse.json();

    // If styling parameters failed, try again with minimal payload
    if (!freepikResponse.ok && freepikData.message?.includes('styling')) {
      console.log('Styling parameters failed, trying with minimal payload...');

      const minimalPayload = {
        prompt: prompt,
        num_images: 1,
        image: {
          size: "landscape_16_9"
        },
        filter_nsfw: true
      };

      freepikResponse = await fetch('https://api.freepik.com/v1/ai/text-to-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-freepik-api-key': apiKey,
        },
        body: JSON.stringify(minimalPayload),
      });

      freepikData = await freepikResponse.json();
    }

    if (!freepikResponse.ok) {
      console.error('Freepik API error:', freepikData);
      return NextResponse.json(
        {
          success: false,
          error: freepikData.message || `Freepik API error: ${freepikResponse.status}`
        },
        { status: freepikResponse.status }
      );
    }

    // Check if we got valid image data
    if (!freepikData.data || !freepikData.data[0] || !freepikData.data[0].base64) {
      console.error('Invalid response from Freepik API:', freepikData);
      return NextResponse.json(
        { success: false, error: 'Invalid response from Freepik API' },
        { status: 500 }
      );
    }

    const imageData = freepikData.data[0];
    const metadata = freepikData.meta;

    console.log('Image generated successfully');

    return NextResponse.json({
      success: true,
      data: {
        base64: imageData.base64,
        has_nsfw: imageData.has_nsfw,
        metadata: {
          width: metadata.image.width,
          height: metadata.image.height,
          size: metadata.image.size,
          prompt: metadata.prompt,
          seed: metadata.seed,
          guidance_scale: metadata.guidance_scale
        }
      }
    });

  } catch (error) {
    console.error('Error generating image with Freepik:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error occurred'
    }, { status: 500 });
  }
}
