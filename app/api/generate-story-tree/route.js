import { NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';

// Initialize the Gemini API client
const apiKey = process.env.GOOGLE_API_KEY;
const genAI = new GoogleGenAI({ apiKey });

// Helper function to fix common JSON issues
function fixJsonString(jsonStr) {
  // Fix common JSON syntax issues
  let fixed = jsonStr;

  // Fix trailing commas
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  // Fix missing commas between properties and objects
  fixed = fixed.replace(/"\s*\n\s*"/g, '",\n"');
  fixed = fixed.replace(/}(\s*)"([^"]+)":/g, '},\n"$2":');
  fixed = fixed.replace(/}(\s*)"/g, '},\n"');

  // Fix missing commas after string values
  fixed = fixed.replace(/"(\s*)"([^"]+)":/g, '",\n"$2":');

  // Fix unescaped quotes in strings
  fixed = fixed.replace(/:\s*"([^"\\]*)\\?([^"\\]*)\\?([^"]*)"(\s*[,}\]])/g, (_, p1, p2, p3, p4) => {
    const content = (p1 + p2 + p3).replace(/"/g, '\\"');
    return `: "${content}"${p4}`;
  });

  // Fix newlines in string values
  fixed = fixed.replace(/:\s*"([^"]*)\n([^"]*)"(\s*[,}])/g, ': "$1\\n$2"$3');

  // Fix single quotes to double quotes (but not inside strings)
  fixed = fixed.replace(/:\s*'([^']*)'/g, ': "$1"');

  // Fix missing quotes around property names
  fixed = fixed.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');

  // Fix boolean and null values that might be quoted
  fixed = fixed.replace(/:\s*"(true|false|null)"/g, ': $1');

  return fixed;
}

// Helper function to safely parse JSON from AI responses
function safeJsonParse(text, context = '') {
  try {
    // Clean the JSON text more thoroughly
    let cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();

    // Remove control characters that break JSON parsing
    cleanedText = cleanedText.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');

    // Replace problematic characters that might appear in text
    cleanedText = cleanedText.replace(/[\u2018\u2019]/g, "'"); // Smart quotes to regular quotes
    cleanedText = cleanedText.replace(/[\u201C\u201D]/g, '"'); // Smart double quotes
    cleanedText = cleanedText.replace(/\u2026/g, '...'); // Ellipsis

    // Try to extract just the JSON object if there's text before or after it
    const jsonObjectMatch = cleanedText.match(/(\{[\s\S]*\})/);
    if (jsonObjectMatch && jsonObjectMatch[1]) {
      cleanedText = jsonObjectMatch[1];
    }

    // Apply JSON fixes
    cleanedText = fixJsonString(cleanedText);

    console.log(`${context} cleaned text:`, cleanedText.substring(0, 300) + '...');
    return JSON.parse(cleanedText);
  } catch (error) {
    console.error(`JSON parsing error in ${context}:`, error);
    console.error('Raw text:', text.substring(0, 800) + '...');

    // Try more aggressive fixing
    try {
      let aggressiveText = text.replace(/```json\n?|\n?```/g, '').trim();

      // Remove all control characters and replace with spaces
      aggressiveText = aggressiveText.replace(/[\x00-\x1F\x7F-\x9F]/g, ' ');

      // Extract JSON object
      const match = aggressiveText.match(/(\{[\s\S]*\})/);
      if (match) {
        let jsonText = match[1];

        // More aggressive JSON repair
        jsonText = fixJsonString(jsonText);

        // Try to fix incomplete JSON by validating structure
        try {
          // Find the position where JSON becomes invalid
          let braceCount = 0;
          let inString = false;
          let escaped = false;
          let lastValidPos = 0;

          for (let i = 0; i < jsonText.length; i++) {
            const char = jsonText[i];

            if (escaped) {
              escaped = false;
              continue;
            }

            if (char === '\\' && inString) {
              escaped = true;
              continue;
            }

            if (char === '"') {
              inString = !inString;
              continue;
            }

            if (!inString) {
              if (char === '{') {
                braceCount++;
              } else if (char === '}') {
                braceCount--;
                if (braceCount === 0) {
                  lastValidPos = i + 1;
                }
              }
            }
          }

          // If we have unmatched braces, truncate to last valid position
          if (braceCount > 0 && lastValidPos > 0) {
            jsonText = jsonText.substring(0, lastValidPos);
          } else if (braceCount > 0) {
            // Add missing closing braces
            jsonText += '}'.repeat(braceCount);
          }
        } catch (structureError) {
          console.error('Structure validation failed:', structureError);
        }

        console.log('Attempting aggressive repair...');
        console.log('Repaired JSON:', jsonText.substring(0, 300) + '...');
        return JSON.parse(jsonText);
      }
    } catch (aggressiveError) {
      console.error('Aggressive repair also failed:', aggressiveError);
    }

    throw error;
  }
}

export async function POST(request) {
  try {
    const { bookTitle, author, changeLocation, whatIfPrompt } = await request.json();

    if (!bookTitle || !author || !changeLocation || !whatIfPrompt) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    const model = 'gemini-2.0-flash-001';

    // Generate the root scenario with image prompt
    const rootPrompt = `
You are a creative fiction writer specializing in alternate timeline stories. Based on the book "${bookTitle}" by ${author}, create an engaging alternate scenario starting from ${changeLocation}.

User's "What If" scenario: ${whatIfPrompt}

Generate a JSON response with the following structure:
{
  "rootScenario": {
    "id": "root",
    "title": "Brief compelling title for this scenario",
    "text": "A complete, engaging narrative of 300-400 words describing the alternate scenario. Make it vivid and immersive.",
    "imagePrompt": "A detailed Freepik image generation prompt describing a key scene from this scenario. Include context that this is an illustration from the book '${bookTitle}' by ${author}. Be very descriptive about the setting, characters, mood, and visual elements.",
    "choices": [
      {
        "id": "choice1",
        "text": "First choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      },
      {
        "id": "choice2",
        "text": "Second choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      },
      {
        "id": "choice3",
        "text": "Third choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      },
      {
        "id": "choice4",
        "text": "Fourth choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      }
    ]
  }
}

Make sure each choice leads to meaningfully different story directions. The scenario should feel authentic to the original book's tone and style.
`;

    const rootResult = await genAI.models.generateContent({
      model,
      contents: [
        { role: 'user', parts: [{ text: rootPrompt }] }
      ],
      config: {
        temperature: 0.7,
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 2048,
      },
    });

    let rootText = '';
    if (rootResult.candidates && rootResult.candidates.length > 0) {
      const candidate = rootResult.candidates[0];
      if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
        rootText = candidate.content.parts[0].text || '';
      }
    }

    let rootScenario;
    try {
      rootScenario = safeJsonParse(rootText, 'Root scenario');
    } catch (parseError) {
      console.error('Error parsing root scenario JSON:', parseError);
      return NextResponse.json(
        { error: 'Failed to parse root scenario response' },
        { status: 500 }
      );
    }

    // Generate Level 2 scenarios for each choice
    const level2Scenarios = {};
    const level2Choices = []; // Store all level 2 choices for level 3 generation

    for (const choice of rootScenario.rootScenario.choices) {
      const level2Prompt = `
Continue the alternate timeline story from "${bookTitle}" by ${author}.

Previous scenario: ${rootScenario.rootScenario.text}

The reader chose: "${choice.text}"

Generate a JSON response with this structure:
{
  "scenario": {
    "id": "${choice.id}",
    "title": "Brief compelling title for this continuation",
    "text": "A complete narrative of 300-400 words continuing from the chosen path. Show the consequences of the choice and develop the story further.",
    "choices": [
      {
        "id": "${choice.id}_1",
        "text": "First choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      },
      {
        "id": "${choice.id}_2",
        "text": "Second choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      },
      {
        "id": "${choice.id}_3",
        "text": "Third choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      },
      {
        "id": "${choice.id}_4",
        "text": "Fourth choice option (30-40 words)",
        "preview": "Brief preview of where this leads (20-30 words)"
      }
    ]
  }
}

Maintain consistency with the original book's tone and the established alternate timeline.
`;

      try {
        const level2Result = await genAI.models.generateContent({
          model,
          contents: [
            { role: 'user', parts: [{ text: level2Prompt }] }
          ],
          config: {
            temperature: 0.7,
            topP: 0.9,
            topK: 40,
            maxOutputTokens: 2048,
          },
        });

        let level2Text = '';
        if (level2Result.candidates && level2Result.candidates.length > 0) {
          const candidate = level2Result.candidates[0];
          if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
            level2Text = candidate.content.parts[0].text || '';
          }
        }

        const level2Data = safeJsonParse(level2Text, `Level 2 scenario for ${choice.id}`);
        level2Scenarios[choice.id] = level2Data.scenario;

        // Store level 2 choices for level 3 generation
        if (level2Data.scenario.choices) {
          level2Choices.push(...level2Data.scenario.choices);
        }
      } catch (error) {
        console.error(`Error generating level 2 scenario for ${choice.id}:`, error);
        // Create a fallback scenario
        const fallbackChoices = [
          { id: `${choice.id}_1`, text: "Continue the adventure", preview: "The story develops further" },
          { id: `${choice.id}_2`, text: "Take a different approach", preview: "A new path emerges" },
          { id: `${choice.id}_3`, text: "Seek help from allies", preview: "Friends join the journey" },
          { id: `${choice.id}_4`, text: "Face the challenge alone", preview: "A solo confrontation awaits" }
        ];

        level2Scenarios[choice.id] = {
          id: choice.id,
          title: "Story Continues...",
          text: "The story continues from your choice. More adventures await as the alternate timeline unfolds with unexpected consequences and new possibilities.",
          choices: fallbackChoices
        };

        // Add fallback choices to level2Choices for level 3 generation
        level2Choices.push(...fallbackChoices);
      }
    }

    // Generate Level 3 scenarios for each level 2 choice
    const level3Scenarios = {};

    for (const level2Choice of level2Choices) {
      // Find the parent level 2 scenario
      const parentScenarioId = level2Choice.id.split('_')[0]; // Extract parent ID (e.g., "choice1" from "choice1_1")
      const parentScenario = level2Scenarios[parentScenarioId];

      if (!parentScenario) continue;

      const level3Prompt = `
Continue the alternate timeline story from "${bookTitle}" by ${author}.

Previous scenario: ${parentScenario.text}

The reader chose: "${level2Choice.text}"

Generate a JSON response with this structure:
{
  "scenario": {
    "id": "${level2Choice.id}",
    "title": "Brief compelling title for this final chapter",
    "text": "A complete narrative of 400-500 words bringing this story branch to a satisfying conclusion. Show the ultimate consequences of all the choices made and provide closure to this alternate timeline.",
    "isEnding": true
  }
}

This should be a concluding chapter that wraps up this particular story branch. Maintain consistency with the original book's tone and the established alternate timeline.
`;

      try {
        const level3Result = await genAI.models.generateContent({
          model,
          contents: [
            { role: 'user', parts: [{ text: level3Prompt }] }
          ],
          config: {
            temperature: 0.7,
            topP: 0.9,
            topK: 40,
            maxOutputTokens: 2048,
          },
        });

        let level3Text = '';
        if (level3Result.candidates && level3Result.candidates.length > 0) {
          const candidate = level3Result.candidates[0];
          if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
            level3Text = candidate.content.parts[0].text || '';
          }
        }

        const level3Data = safeJsonParse(level3Text, `Level 3 scenario for ${level2Choice.id}`);
        level3Scenarios[level2Choice.id] = level3Data.scenario;
      } catch (error) {
        console.error(`Error generating level 3 scenario for ${level2Choice.id}:`, error);
        // Create a fallback ending scenario
        level3Scenarios[level2Choice.id] = {
          id: level2Choice.id,
          title: "The Story Concludes",
          text: "The alternate timeline reaches its conclusion. The choices made have led to this moment, where the consequences of change ripple through the narrative, creating a new ending to a familiar tale. Though different from the original, this path has its own meaning and resolution.",
          isEnding: true
        };
      }
    }

    const storyTree = {
      success: true,
      root: rootScenario.rootScenario,
      level2: level2Scenarios,
      level3: level3Scenarios,
      bookInfo: {
        title: bookTitle,
        author: author,
        changeLocation: changeLocation,
        whatIfPrompt: whatIfPrompt
      }
    };

    return NextResponse.json(storyTree);

  } catch (error) {
    console.error('Error generating story tree:', error);
    return NextResponse.json(
      { error: 'Failed to generate story tree' },
      { status: 500 }
    );
  }
}
