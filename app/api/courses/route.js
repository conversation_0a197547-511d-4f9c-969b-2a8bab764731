import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, orderBy, limit, startAfter, doc, getDoc, setDoc } from 'firebase/firestore';

// Get all courses with filtering and pagination
export async function GET(request) {
  console.log('GET /api/courses', request.url);
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const difficulty = searchParams.get('difficulty');
    const targetAge = searchParams.get('targetAge');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const lastCourseId = searchParams.get('lastCourseId');

    let courseQuery = query(
      collection(db, 'courses'),
      where('status', '==', 'published')
    );

    // Apply filters
    if (category) {
      courseQuery = query(courseQuery, where('category', '==', category));
    }
    if (difficulty) {
      courseQuery = query(courseQuery, where('difficulty', '==', parseInt(difficulty)));
    }
    if (targetAge) {
      courseQuery = query(courseQuery, where('targetAge', 'array-contains', parseInt(targetAge)));
    }

    // Apply pagination
    courseQuery = query(courseQuery, orderBy('createdAt', 'desc'), limit(pageSize));
    if (lastCourseId) {
      const lastCourseDoc = await getDoc(doc(db, 'courses', lastCourseId));
      if (lastCourseDoc.exists()) {
        courseQuery = query(courseQuery, startAfter(lastCourseDoc));
      }
    }

    const coursesSnapshot = await getDocs(courseQuery);
    console.log('coursesSnapshot:', coursesSnapshot);
    const courses = coursesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json({
      success: true,
      courses,
      lastCourseId: courses.length > 0 ? courses[courses.length - 1].id : null
    });
  } catch (error) {
    console.log('GET /api/courses error:', error.message);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Create new course (admin/teacher only)
export async function POST(request) {
  try {
    const courseData = await request.json();
    const { title, description, thumbnail, category, difficulty, targetAge, modules } = courseData;
    // "createdAt": {
    //             "seconds": 1740677358,
    //             "nanoseconds": 841000000
    //         // },

    const createdAt= new Date().toISOString();
    

    // Validate required fields
    if (!title || !description || !category || !difficulty || !targetAge) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }

    const courseRef = doc(collection(db, 'courses'));
    await setDoc(courseRef, {
      title,
      description,
      thumbnail,
      category,
      difficulty: parseInt(difficulty),
      targetAge: Array.isArray(targetAge) ? targetAge.map(age => parseInt(age)) : [parseInt(targetAge)],
      modules: modules || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'published'
    });

    const newCourse = await getDoc(courseRef);
    return NextResponse.json({
      success: true,
      course: {
        id: newCourse.id,
        ...newCourse.data()
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}