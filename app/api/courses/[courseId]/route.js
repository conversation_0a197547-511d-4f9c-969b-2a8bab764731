import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, deleteDoc } from 'firebase/firestore';

// Get course details
export async function GET(request, { params }) {
  try {
    console.log(params);
    const { courseId } = await params;
    
    const courseDoc = await getDoc(doc(db, 'courses', courseId));
    if (!courseDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Course not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      course: {
        id: courseDoc.id,
        ...courseDoc.data()
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Update course (admin/teacher only)
export async function PUT(request, { params }) {
  try {
    const { courseId } = params;
    const updates = await request.json();

    const allowedUpdates = [
      'title',
      'description',
      'thumbnail',
      'category',
      'difficulty',
      'targetAge',
      'modules',
      'status'
    ];

    // Filter out non-allowed updates
    const filteredUpdates = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj, key) => {
        obj[key] = updates[key];
        return obj;
      }, {});

    const courseRef = doc(db, 'courses', courseId);
    await updateDoc(courseRef, {
      ...filteredUpdates,
      updatedAt: new Date().toISOString()
    });

    const updatedDoc = await getDoc(courseRef);
    return NextResponse.json({
      success: true,
      course: {
        id: updatedDoc.id,
        ...updatedDoc.data()
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Delete course (admin only)
export async function DELETE(request, { params }) {
  try {
    const { courseId } = params;
    
    await deleteDoc(doc(db, 'courses', courseId));
    
    return NextResponse.json({
      success: true,
      message: 'Course deleted successfully'
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}