import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc, updateDoc, increment } from 'firebase/firestore';

export async function POST(request) {
  try {
    const { userId, courseId, moduleId, type, data } = await request.json();

    if (!userId || !type) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }

    const userProgressRef = doc(db, 'userProgress', `${userId}_${courseId}`);
    const userRef = doc(db, 'users', userId);

    // Start a batch write
    const batch = db.batch();

    switch (type) {
      case 'module_completion':
        await handleModuleCompletion(userProgressRef, userRef, moduleId, batch);
        break;
      case 'game_score':
        await handleGameScore(userProgressRef, userRef, data, batch);
        break;
      case 'quiz_completion':
        await handleQuizCompletion(userProgressRef, userRef, data, batch);
        break;
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid progress type'
        }, { status: 400 });
    }

    // Commit the batch
    await batch.commit();

    // Get updated progress
    const updatedProgress = await getDoc(userProgressRef);
    const updatedUser = await getDoc(userRef);

    return NextResponse.json({
      success: true,
      progress: updatedProgress.data(),
      userProgress: updatedUser.data().progress
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

async function handleModuleCompletion(progressRef, userRef, moduleId, batch) {
  const progressDoc = await getDoc(progressRef);
  const progress = progressDoc.exists() ? progressDoc.data() : {
    completedModules: [],
    gameScores: {},
    quizResults: []
  };

  if (!progress.completedModules.includes(moduleId)) {
    progress.completedModules.push(moduleId);
    batch.update(progressRef, {
      completedModules: progress.completedModules,
      currentModule: moduleId,
      updatedAt: new Date().toISOString()
    });

    // Award XP for module completion
    batch.update(userRef, {
      'progress.xp': increment(50)
    });
  }
}

async function handleGameScore(progressRef, userRef, gameData, batch) {
  const { gameId, score } = gameData;
  const progressDoc = await getDoc(progressRef);
  const progress = progressDoc.exists() ? progressDoc.data() : {
    completedModules: [],
    gameScores: {},
    quizResults: []
  };

  const currentGameScore = progress.gameScores[gameId] || { highScore: 0, attempts: 0 };
  const isNewHighScore = score > currentGameScore.highScore;

  batch.update(progressRef, {
    [`gameScores.${gameId}`]: {
      highScore: isNewHighScore ? score : currentGameScore.highScore,
      attempts: (currentGameScore.attempts || 0) + 1,
      lastPlayed: new Date().toISOString()
    },
    updatedAt: new Date().toISOString()
  });

  if (isNewHighScore) {
    batch.update(userRef, {
      'progress.xp': increment(25)
    });
  }
}

async function handleQuizCompletion(progressRef, userRef, quizData, batch) {
  const { quizId, score } = quizData;
  const progressDoc = await getDoc(progressRef);
  const progress = progressDoc.exists() ? progressDoc.data() : {
    completedModules: [],
    gameScores: {},
    quizResults: []
  };

  const quizResult = {
    quizId,
    score,
    completedAt: new Date().toISOString()
  };

  batch.update(progressRef, {
    quizResults: [...(progress.quizResults || []), quizResult],
    updatedAt: new Date().toISOString()
  });

  // Award XP based on quiz score
  const xpEarned = Math.floor(score * 0.5); // 50% of score as XP
  batch.update(userRef, {
    'progress.xp': increment(xpEarned)
  });
}