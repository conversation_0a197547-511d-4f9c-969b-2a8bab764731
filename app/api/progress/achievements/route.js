import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, getDocs } from 'firebase/firestore';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    const achievementIds = userDoc.data().progress?.achievementIds || [];
    const achievements = [];

    // Get all achievements
    const achievementsSnapshot = await getDocs(collection(db, 'achievements'));
    const allAchievements = achievementsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      earned: achievementIds.includes(doc.id)
    }));

    return NextResponse.json({
      success: true,
      achievements: allAchievements
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}