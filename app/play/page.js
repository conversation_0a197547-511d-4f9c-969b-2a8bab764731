'use client';

import { useEffect, useRef, useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function GamePage() {
  const canvasRef = useRef(null);
  const [gameLoaded, setGameLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [error, setError] = useState(null);
  const engineRef = useRef(null);

  useEffect(() => {
    let engine = null;
    let scriptElement = null;

    // ✅ Define Module FIRST — critical
    window.Module = {
      locateFile: (path) => {
        console.log('[Godot locateFile]', path);
        return `/game/${path}`;
      }
    };

    const loadGame = async () => {
      try {
        scriptElement = document.createElement('script');
        scriptElement.src = '/game/index.js';
        scriptElement.onload = () => {
          initializeGame();
        };
        scriptElement.onerror = () => {
          setError('Failed to load game engine.');
        };
        document.head.appendChild(scriptElement);
      } catch (err) {
        setError('Failed to initialize game: ' + err.message);
      }
    };

    const initializeGame = () => {
      const GODOT_CONFIG = {
        executable: 'index',
        canvasResizePolicy: 2,
        fileSizes: {
          'index.pck': 88096,
          'index.wasm': 49282035
        },
        focusCanvas: true
      };

      engine = new window.Engine(GODOT_CONFIG);
      engineRef.current = engine;

      engine.startGame({
        canvas: canvasRef.current,
        onProgress: (current, total) => {
          if (total > 0) {
            setLoadingProgress((current / total) * 100);
          }
        }
      }).then(() => {
        setGameLoaded(true);
      }).catch(err => {
        setError('Failed to start game: ' + err.message);
      });
    };

    loadGame();

    return () => {
      if (engineRef.current?.requestQuit) engineRef.current.requestQuit();
      if (scriptElement?.parentNode) scriptElement.parentNode.removeChild(scriptElement);
    };
  }, []);


  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex items-center justify-between">
        <Link
          href="/"
          className="flex items-center text-white hover:text-gray-300 transition-colors"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to The Money Tales
        </Link>
        <h1 className="text-white text-xl font-bold">What If Game</h1>
      </div>

      {/* Game Container */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="relative w-full max-w-6xl">
          {/* Loading Screen */}
          {!gameLoaded && !error && (
            <div className="game-loading">
              <div className="text-white text-xl mb-4">Loading Game...</div>
              <div className="w-64 h-2 bg-gray-600 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-500 transition-all duration-300"
                  style={{ width: `${loadingProgress}%` }}
                />
              </div>
              <div className="text-gray-300 mt-2">{Math.round(loadingProgress)}%</div>
            </div>
          )}

          {/* Error Screen */}
          {error && (
            <div className="game-error">
              <div className="text-white text-xl mb-4">Error Loading Game</div>
              <div className="text-red-200 text-center max-w-md">
                {error}
              </div>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          )}

          {/* Game Canvas */}
          <canvas
            ref={canvasRef}
            id="canvas"
            className="game-canvas"
          >
            HTML5 canvas appears to be unsupported in the current browser.
            Please try updating or use a different browser.
          </canvas>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-900 p-4 text-center text-gray-400">
        <p>Use WASD or arrow keys to move. Click to interact.</p>
        <div className="mt-2 text-sm">
          <span>Game Status: </span>
          <span className={gameLoaded ? 'text-green-400' : 'text-yellow-400'}>
            {gameLoaded ? 'Loaded' : error ? 'Error' : 'Loading...'}
          </span>
        </div>
      </div>
    </div>
  );
}
