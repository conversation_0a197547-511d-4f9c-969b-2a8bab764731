'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { generateSceneImage } from '../utils/freepikService';

export default function AlternateScenarioDisplay({ scenario, isLoading, error }) {
  const [currentScreen, setCurrentScreen] = useState(1);
  const [generatedImage, setGeneratedImage] = useState(null);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageError, setImageError] = useState(null);

  // Reset image state when scenario changes and generate new image
  useEffect(() => {
    // Reset image state when scenario changes
    setGeneratedImage(null);
    setImageError(null);

    const generateImage = async () => {
      if (scenario?.data?.scene_visuals && !imageLoading) {
        setImageLoading(true);
        setImageError(null);

        try {
          const result = await generateSceneImage(scenario.data.scene_visuals);

          if (result.success) {
            setGeneratedImage(result.data);
          } else {
            setImageError(result.error || 'Failed to generate image');
          }
        } catch (error) {
          console.error('Error generating image:', error);
          setImageError('Error generating image: ' + error.message);
        } finally {
          setImageLoading(false);
        }
      }
    };

    // Add a small delay to ensure state is reset before generating
    const timeoutId = setTimeout(generateImage, 100);

    return () => clearTimeout(timeoutId);
  }, [scenario?.data?.scene_visuals, imageLoading]); // Include imageLoading dependency

  if (isLoading) {
    return (
      <div className="p-6 bg-gray-800 rounded-lg shadow-lg">
        <h3 className="text-xl font-semibold text-gray-300 mb-4">Generating alternate scenario...</h3>
        <div className="space-y-4">
          <div className="h-4 bg-gray-700 rounded w-3/4 animate-pulse"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2 animate-pulse"></div>
          <div className="h-4 bg-gray-700 rounded w-5/6 animate-pulse"></div>
          <div className="h-4 bg-gray-700 rounded w-2/3 animate-pulse"></div>
          <div className="h-20 bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div className="mt-6 text-gray-400 text-sm">
          <p>Using Google&apos;s Gemini AI to create an alternate timeline scenario based on your analysis...</p>
          <p className="mt-2">This may take a moment as we craft a creative and engaging narrative.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-8 p-6 bg-red-900/30 border border-red-700 rounded-lg shadow-lg">
        <h3 className="text-xl font-semibold text-red-300 mb-4">Error Generating Scenario</h3>
        <p className="text-red-200">{error}</p>
      </div>
    );
  }

  if (!scenario || !scenario.data) {
    return null;
  }

  const { data } = scenario;
  const { scenario: scenarioInfo, scene_visuals, screens } = data;

  const handleNextScreen = () => {
    if (currentScreen < screens.length) {
      setCurrentScreen(currentScreen + 1);
    }
  };

  const handlePrevScreen = () => {
    if (currentScreen > 1) {
      setCurrentScreen(currentScreen - 1);
    }
  };

  return (
    <div className="mt-8 p-6 bg-gray-800 rounded-lg shadow-lg">
      <h3 className="text-2xl font-bold text-primary-green mb-4">{scenarioInfo.title}</h3>

      {/* Generated Image */}
      <div className="mb-6">
        {imageLoading && (
          <div className="w-full h-64 bg-gray-700/50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-green mx-auto mb-2"></div>
              <p className="text-gray-400 text-sm">Generating scene image...</p>
            </div>
          </div>
        )}

        {imageError && (
          <div className="w-full p-4 bg-red-900/30 border border-red-700 rounded-lg">
            <p className="text-red-300 text-sm">Failed to generate image: {imageError}</p>
          </div>
        )}

        {generatedImage && (
          <div className="relative">
            <Image
              src={`data:image/png;base64,${generatedImage.base64}`}
              alt="Generated scene visualization"
              width={800}
              height={400}
              className="w-full h-auto rounded-lg shadow-lg"
              style={{ maxHeight: '400px', objectFit: 'cover' }}
            />
            <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
              Generated by Freepik AI
            </div>
          </div>
        )}
      </div>

      {/* Scene Visuals */}
      <div className="mb-6 p-4 bg-gray-700/50 rounded-lg">
        <h4 className="text-lg font-semibold text-gray-300 mb-2">Scene Visuals</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h5 className="text-sm font-medium text-gray-400">Background</h5>
            <p className="text-gray-200 text-sm">{scene_visuals.background_description}</p>
          </div>
          <div>
            <h5 className="text-sm font-medium text-gray-400">Character</h5>
            <p className="text-gray-200 text-sm">{scene_visuals.character_state}</p>
          </div>
          <div>
            <h5 className="text-sm font-medium text-gray-400">Mood</h5>
            <p className="text-gray-200 text-sm">{scene_visuals.scene_mood}</p>
          </div>
        </div>
      </div>

      {/* Scenario Text */}
      <div className="mb-6 p-4 bg-gray-700/50 rounded-lg">
        <h4 className="text-lg font-semibold text-gray-300 mb-2">Scenario</h4>
        <div className="prose prose-invert max-w-none">
          {screens.map((screen) => (
            <div
              key={screen.id}
              className={`transition-opacity duration-300 ${screen.id === currentScreen ? 'opacity-100' : 'hidden'}`}
            >
              {screen.text && <p className="text-gray-200">{screen.text}</p>}
              {screen.choice_text && <p className="text-gray-200">{screen.choice_text}</p>}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="flex justify-between">
        <button
          onClick={handlePrevScreen}
          disabled={currentScreen === 1}
          className={`px-4 py-2 rounded-md ${currentScreen === 1 ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-primary-green text-white hover:bg-primary-green-dark'}`}
        >
          Previous
        </button>
        <div className="text-gray-400">
          {currentScreen} of {screens.length}
        </div>
        <button
          onClick={handleNextScreen}
          disabled={currentScreen === screens.length}
          className={`px-4 py-2 rounded-md ${currentScreen === screens.length ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-primary-green text-white hover:bg-primary-green-dark'}`}
        >
          Next
        </button>
      </div>

      {/* Raw JSON Display (for debugging) */}
      {scenario.rawResponse && (
        <div className="mt-6 p-4 bg-gray-900 rounded-lg overflow-x-auto hidden">
          <h4 className="text-sm font-semibold text-gray-400 mb-2">Raw Response</h4>
          <pre className="text-xs text-gray-400">{scenario.rawResponse}</pre>
        </div>
      )}
    </div>
  );
}
