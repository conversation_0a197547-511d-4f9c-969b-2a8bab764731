'use client';

import { useState } from 'react';

export default function EmbeddingsDisplay({
  chunks,
  isGenerating,
  onGenerateEmbeddings,
  prompt,
  onAnalyzePrompt,
  isAnalyzing,
  embeddingProgress
}) {
  const [showFullEmbeddings, setShowFullEmbeddings] = useState(false);
  const [expandedChunk, setExpandedChunk] = useState(null);

  // Format embedding for display
  const formatEmbedding = (embedding) => {
    if (!embedding) return 'No embedding generated';

    // Convert to array if it's not already
    const embeddingArray = Array.from(embedding);

    if (!showFullEmbeddings) {
      // Show only first 5 and last 5 values
      const firstFive = embeddingArray.slice(0, 5);
      const lastFive = embeddingArray.slice(-5);
      return `[${firstFive.map(v => v.toFixed(4)).join(', ')}, ... (${embeddingArray.length - 10} more values) ..., ${lastFive.map(v => v.toFixed(4)).join(', ')}]`;
    }

    // Show full embedding
    return `[${embeddingArray.map(v => v.toFixed(4)).join(', ')}]`;
  };

  if (!chunks || chunks.length === 0) {
    return null;
  }

  const hasEmbeddings = chunks.some(chunk => chunk.embedding);

  return (
    <div className="mt-8 bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="font-libre text-2xl font-bold text-text-primary">Embeddings Visualization</h2>

        {!hasEmbeddings && (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (!isGenerating) {
                onGenerateEmbeddings();
              }
            }}
            disabled={isGenerating}
            className={`px-4 py-2 rounded-md font-medium transition-all duration-300 ${
              isGenerating
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-primary-green text-white hover:bg-primary-green-dark'
            }`}
          >
            {isGenerating ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating Embeddings...
              </span>
            ) : (
              'Generate Embeddings'
            )}
          </button>
        )}

        {hasEmbeddings && (
          <div className="flex items-center space-x-4">
            {prompt && (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (!isAnalyzing) {
                    onAnalyzePrompt(chunks);
                  }
                }}
                disabled={isAnalyzing}
                className={`px-4 py-2 rounded-md font-medium transition-all duration-300 ${
                  isAnalyzing
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {isAnalyzing ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Analyzing...
                  </span>
                ) : (
                  'Analyze Prompt'
                )}
              </button>
            )}

            <label className="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={showFullEmbeddings}
                onChange={() => setShowFullEmbeddings(!showFullEmbeddings)}
                className="sr-only peer"
              />
              <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-green"></div>
              <span className="ms-3 text-sm font-medium text-gray-600">
                Show Full Embeddings
              </span>
            </label>
          </div>
        )}
      </div>

      {isGenerating && (
        <div className="py-8">
          <div className="flex items-center justify-center mb-4">
            <svg className="animate-spin mr-3 h-8 w-8 text-primary-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-lg text-primary-green">Generating embeddings...</span>
          </div>

          {embeddingProgress && embeddingProgress.total > 0 && (
            <div className="max-w-md mx-auto">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress: {embeddingProgress.current} / {embeddingProgress.total}</span>
                <span>{Math.round((embeddingProgress.current / embeddingProgress.total) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-primary-green h-2.5 rounded-full transition-all duration-300"
                  style={{ width: `${(embeddingProgress.current / embeddingProgress.total) * 100}%` }}
                ></div>
              </div>
              {embeddingProgress.chunkId && (
                <div className="text-xs text-gray-500 mt-2 text-center">
                  Processing: {embeddingProgress.chunkId}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {hasEmbeddings && (
        <div className="space-y-4 mt-4">
          <p className="text-gray-600 mb-2">
            Generated embeddings for {chunks.length} chunks using Xenova TransformerJS.
          </p>

          {chunks.map((chunk, index) => (
            <div key={chunk.id || index} className="border border-gray-200 rounded-md p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium text-lg">
                  {chunk.title || `Chunk ${index + 1}`}
                </h3>
                <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-md">
                  {chunk.startPage && chunk.endPage
                    ? `Pages ${chunk.startPage}-${chunk.endPage}`
                    : `ID: ${chunk.id || index}`}
                </span>
              </div>

              <div className="mb-2">
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Content Preview:</span>{' '}
                  {chunk.content
                    ? (expandedChunk === index
                        ? chunk.content
                        : chunk.content.substring(0, 150) + (chunk.content.length > 150 ? '...' : ''))
                    : 'No content available'}
                </p>
                {chunk.content && chunk.content.length > 150 && (
                  <button
                    onClick={() => setExpandedChunk(expandedChunk === index ? null : index)}
                    className="text-xs text-primary-green mt-1 hover:underline"
                  >
                    {expandedChunk === index ? 'Show less' : 'Show more'}
                  </button>
                )}
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 mb-1">
                  Embedding ({chunk.embedding ? chunk.embedding.length : 0} dimensions):
                </p>
                <div className="bg-gray-50 p-3 rounded-md overflow-x-auto">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                    {chunk.embedding ? formatEmbedding(chunk.embedding) : 'No embedding generated'}
                  </pre>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {!hasEmbeddings && !isGenerating && (
        <div className="text-center py-8 text-gray-500">
          <p>Click the "Generate Embeddings" button to create vector embeddings for your PDF chunks.</p>
          <p className="text-sm mt-2">This process runs entirely in your browser using Xenova TransformerJS.</p>
        </div>
      )}
    </div>
  );
}
