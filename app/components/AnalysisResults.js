'use client';

export default function AnalysisResults({ results, prompt }) {
  if (!results || results.length === 0) {
    return null;
  }

  // Format embedding for display
  const formatEmbedding = (embedding) => {
    if (!embedding) return 'No embedding generated';
    
    // Convert to array if it's not already
    const embeddingArray = Array.from(embedding);
    
    // Show only first 5 and last 5 values
    const firstFive = embeddingArray.slice(0, 5);
    const lastFive = embeddingArray.slice(-5);
    return `[${firstFive.map(v => v.toFixed(4)).join(', ')}, ... (${embeddingArray.length - 10} more values) ..., ${lastFive.map(v => v.toFixed(4)).join(', ')}]`;
  };

  return (
    <div className="mt-8 bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="font-libre text-2xl font-bold text-text-primary">Analysis Results</h2>
        <span className="text-sm bg-primary-green/10 px-3 py-1 rounded-full text-primary-green">
          {results.length} results for: &quot;{prompt}&quot;
        </span>
      </div>

      <div className="space-y-6 mt-4">
        {results.slice(0, 5).map((result, index) => (
          <div key={result.id || index} className="border border-gray-200 rounded-md p-4 hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium text-lg">
                {index + 1}. {result.title || `Result ${index + 1}`}
              </h3>
              <div className="flex flex-col items-end">
                <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-md">
                  Score: {result.score.toFixed(4)}
                </span>
                <span className="text-xs text-gray-500 mt-1">
                  Pages {result.startPage}-{result.endPage}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-3">
              <div className="bg-gray-50 p-2 rounded-md">
                <span className="text-xs font-medium text-gray-500">Semantic Similarity</span>
                <div className="text-sm font-medium mt-1">
                  {result.details.similarityScore.toFixed(4)}
                  <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                    <div 
                      className="bg-blue-600 h-1.5 rounded-full" 
                      style={{ width: `${result.details.similarityScore * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-2 rounded-md">
                <span className="text-xs font-medium text-gray-500">Word Match</span>
                <div className="text-sm font-medium mt-1">
                  {result.details.wordMatchScore.toFixed(4)}
                  <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                    <div 
                      className="bg-green-500 h-1.5 rounded-full" 
                      style={{ width: `${result.details.wordMatchScore * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-2 rounded-md">
                <span className="text-xs font-medium text-gray-500">Sentiment Match</span>
                <div className="text-sm font-medium mt-1">
                  {result.details.sentimentScore.toFixed(4)}
                  <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                    <div 
                      className="bg-purple-500 h-1.5 rounded-full" 
                      style={{ width: `${result.details.sentimentScore * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-3">
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>Query Sentiment: {result.details.querySentiment.label} ({result.details.querySentiment.score.toFixed(4)})</span>
                <span>Content Sentiment: {result.details.chunkSentiment.label} ({result.details.chunkSentiment.score.toFixed(4)})</span>
              </div>
            </div>

            <div className="mb-3">
              <h4 className="text-sm font-medium text-gray-700 mb-1">Content Preview:</h4>
              <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md max-h-32 overflow-y-auto">
                {result.content ? (
                  result.content.length > 500 
                    ? result.content.substring(0, 500) + '...' 
                    : result.content
                ) : 'No content available'}
              </p>
            </div>

            {result.path && (
              <div className="text-xs text-gray-500">
                Path: {result.path.join(' > ')}
              </div>
            )}
          </div>
        ))}

        {results.length >10 && (
          <div className="text-center text-gray-500 text-sm mt-4">
            Showing top 10 results of {results.length} total matches
          </div>
        )}
      </div>
    </div>
  );
}
