# Godot Web Game Integration

This document describes how the Godot web game has been integrated into "the_money_tales" Next.js project.

## Integration Overview

The Godot web game has been successfully integrated into the Next.js application with the following structure:

### File Structure
```
the_money_tales/
├── app/
│   ├── game/
│   │   ├── page.js          # Main game page component
│   │   └── layout.js        # Game-specific layout
│   └── page.js              # Updated main page with game link
├── public/
│   └── game/                # All Godot game assets
│       ├── index.js         # Godot engine
│       ├── index.wasm       # WebAssembly binary
│       ├── index.pck        # Game data package
│       ├── index.worker.js  # Web worker
│       ├── index.audio.worklet.js
│       ├── index.service.worker.js
│       ├── index.manifest.json
│       ├── index.offline.html
│       └── *.png            # Game icons and images
└── app/globals.css          # Updated with game-specific styles
```

### Key Features

1. **Responsive Design**: The game canvas adapts to different screen sizes while maintaining aspect ratio
2. **Loading States**: Visual feedback during game loading with progress indicators
3. **Error Handling**: Graceful error handling with retry functionality
4. **Navigation**: Seamless integration with the main app navigation
5. **Asset Management**: Proper path resolution for all game assets

### Technical Implementation

#### Game Component (`app/game/page.js`)
- React component that loads and initializes the Godot engine
- Handles asset path resolution for the Next.js environment
- Manages loading states and error conditions
- Provides cleanup on component unmount

#### Asset Loading
- All game assets are served from `/public/game/` directory
- Service worker updated to work with new paths
- Proper MIME types and caching for optimal performance

#### Styling
- Game-specific CSS classes added to `globals.css`
- Responsive canvas that maintains 16:9 aspect ratio
- Loading and error state styling

## How to Access the Game

### Development Environment
1. Start the development server:
   ```bash
   cd the_money_tales
   npm run dev
   ```

2. Open your browser and navigate to:
   - Main app: `http://localhost:3000`
   - Game directly: `http://localhost:3000/game`

3. Click the "Play Game" button in the header navigation

### Production Environment
1. Build the application:
   ```bash
   npm run build
   ```

2. Start the production server:
   ```bash
   npm start
   ```

3. Navigate to your domain and access the game via the navigation

## Game Controls

- **Movement**: WASD keys or Arrow keys
- **Interaction**: Mouse click
- **Fullscreen**: F11 (browser default)

## Browser Compatibility

The game requires a modern browser with support for:
- WebAssembly (WASM)
- Web Workers
- Canvas API
- ES6+ JavaScript features

### Supported Browsers
- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## Troubleshooting

### Common Issues

1. **Game doesn't load**
   - Check browser console for errors
   - Ensure all assets are accessible at `/game/` path
   - Verify WebAssembly support in browser

2. **Loading stuck at 0%**
   - Check network tab for failed asset requests
   - Verify service worker is not blocking requests
   - Clear browser cache and reload

3. **Performance issues**
   - Close other browser tabs
   - Disable browser extensions
   - Check system resources

### Debug Mode
To enable debug logging, open browser console and run:
```javascript
localStorage.setItem('debug', 'true');
```

## Future Enhancements

Potential improvements for the game integration:

1. **Save System**: Integrate with the app's user system for game saves
2. **Achievements**: Connect game achievements with the main app
3. **Analytics**: Track game usage and performance metrics
4. **Mobile Optimization**: Enhanced touch controls for mobile devices
5. **Progressive Loading**: Implement progressive asset loading for faster initial load

## Support

For issues related to the game integration, check:
1. Browser console for JavaScript errors
2. Network tab for failed asset requests
3. Server logs for backend issues

The game is fully integrated and should work seamlessly within the Next.js application environment.
